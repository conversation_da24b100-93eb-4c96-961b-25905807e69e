 WARN  Unsupported engine: wanted: {"node":"20.19.2"} (current: {"node":"v22.14.0","pnpm":"10.8.1"})

> kilo-code@4.35.0 check-types C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\src
> tsc --noEmit

api/index.ts(110,4): error TS2322: Type 'MicrochipHandler' is not assignable to type 'ApiHandler'.
  Types of property 'createMessage' are incompatible.
    Type '(systemPrompt: string, messages: MessageParam[], cacheKey?: string | undefined) => ApiStream' is not assignable to type '(systemPrompt: string, messages: MessageParam[], metadata?: ApiHandlerCreateMessageMetadata | undefined) => ApiStream'.
      Types of parameters 'cacheKey' and 'metadata' are incompatible.
        Type 'ApiHandlerCreateMessageMetadata | undefined' is not assignable to type 'string | undefined'.
          Type 'ApiHandlerCreateMessageMetadata' is not assignable to type 'string'.
api/providers/__tests__/microchip.test.ts(5,81): error TS2307: Cannot find module '@roo-code/types/providers/microchip' or its corresponding type declarations.
api/providers/microchip.ts(16,81): error TS2307: Cannot find module '@roo-code/types/providers/microchip' or its corresponding type declarations.
api/providers/microchip.ts(95,18): error TS2416: Property 'createMessage' in type 'MicrochipHandler' is not assignable to the same property in base type 'BaseProvider'.
  Type '(systemPrompt: string, messages: MessageParam[], cacheKey?: string | undefined) => ApiStream' is not assignable to type '(systemPrompt: string, messages: MessageParam[], metadata?: ApiHandlerCreateMessageMetadata | undefined) => ApiStream'.
    Types of parameters 'cacheKey' and 'metadata' are incompatible.
      Type 'ApiHandlerCreateMessageMetadata | undefined' is not assignable to type 'string | undefined'.
        Type 'ApiHandlerCreateMessageMetadata' is not assignable to type 'string'.
 ELIFECYCLE  Command failed with exit code 2.
