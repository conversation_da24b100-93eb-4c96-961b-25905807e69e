import { escapeSpaces, convertToMentionPath } from "../path-mentions"

describe("Path Mentions Utilities", () => {
	describe("escapeSpaces", () => {
		it("should replace spaces with escaped spaces", () => {
			expect(escapeSpaces("file with spaces.txt")).toBe("file\\ with\\ spaces.txt")
			expect(escapeSpaces("/path/to/another file/")).toBe("/path/to/another\\ file/")
			expect(escapeSpaces("single space")).toBe("single\\ space")
		})

		it("should handle paths without spaces", () => {
			expect(escapeSpaces("file_without_spaces.txt")).toBe("file_without_spaces.txt")
			expect(escapeSpaces("/path/to/normal/file")).toBe("/path/to/normal/file")
		})

		it("should handle multiple spaces", () => {
			expect(escapeSpaces("a b c d.txt")).toBe("a\\ b\\ c\\ d.txt")
		})

		it("should handle leading/trailing spaces", () => {
			expect(escapeSpaces(" leading space")).toBe("\\ leading\\ space")
			expect(escapeSpaces("trailing space ")).toBe("trailing\\ space\\ ")
		})

		it("should handle empty string", () => {
			expect(escapeSpaces("")).toBe("")
		})

		it("should not affect already escaped spaces", () => {
			// This function assumes input spaces are not already escaped
			// The function will re-escape the backslashes, resulting in double-escaped spaces
			expect(escapeSpaces("file\\ with\\ spaces.txt")).toBe("file\\\\ with\\\\ spaces.txt")
		})

		it("should not escape other characters", () => {
			expect(escapeSpaces("path/with/slashes")).toBe("path/with/slashes")
			expect(escapeSpaces("file-with-hyphens.txt")).toBe("file-with-hyphens.txt")
		})
	})

	describe("convertToMentionPath", () => {
		const MOCK_CWD_POSIX = "/Users/<USER>/project"
		const MOCK_CWD_WIN = "C:\\Users\\<USER>\\project"

		it("should convert absolute posix path within cwd to relative mention path and escape spaces", () => {
			const absPath = "/Users/<USER>/project/src/file with spaces.ts"
			expect(convertToMentionPath(absPath, MOCK_CWD_POSIX)).toBe("@/src/file\\ with\\ spaces.ts")
		})

		it("should convert absolute windows path within cwd to relative mention path and escape spaces", () => {
			const absPath = "C:\\Users\\<USER>\\project\\src\\file with spaces.ts"
			expect(convertToMentionPath(absPath, MOCK_CWD_WIN)).toBe("@/src/file\\ with\\ spaces.ts")
		})

		it("should handle paths already relative to cwd (though input is usually absolute)", () => {
			const relPath = "src/another file.js" // Assuming this might be passed somehow
			// It won't match startsWith(cwd), so it should return the original path (but normalized)
			expect(convertToMentionPath(relPath, MOCK_CWD_POSIX)).toBe("src/another file.js")
		})

		it("should handle paths outside cwd by returning the original path (normalized)", () => {
			const absPath = "/Users/<USER>/file.txt"
			expect(convertToMentionPath(absPath, MOCK_CWD_POSIX)).toBe("/Users/<USER>/file.txt")
			// Since we can't control the implementation of path normalization in this test,
			// let's accept either form of path separators (/ or \) for the Windows path test
			const winPath = "D:\\another\\folder\\file.txt"
			const result = convertToMentionPath(winPath, MOCK_CWD_WIN)
			// Check that the path was returned without being converted to a mention
			expect(result.startsWith("@")).toBe(false)
			// Check the path contains the expected components regardless of separator
			expect(result.toLowerCase()).toContain("d:")
			expect(result.toLowerCase()).toContain("another")
			expect(result.toLowerCase()).toContain("folder")
			expect(result.toLowerCase()).toContain("file.txt")
		})

		it("should handle paths with no spaces correctly", () => {
			const absPath = "/Users/<USER>/project/src/normal.ts"
			expect(convertToMentionPath(absPath, MOCK_CWD_POSIX)).toBe("@/src/normal.ts")
		})

		it("should add leading slash if missing after cwd removal", () => {
			const absPath = "/Users/<USER>/projectfile.txt" // Edge case: file directly in project root
			const cwd = "/Users/<USER>/project"
			expect(convertToMentionPath(absPath, cwd)).toBe("@/file.txt") // Should still add '/'
		})

		it("should handle cwd with trailing slash", () => {
			const absPath = "/Users/<USER>/project/src/file with spaces.ts"
			const cwdWithSlash = MOCK_CWD_POSIX + "/"
			expect(convertToMentionPath(absPath, cwdWithSlash)).toBe("@/src/file\\ with\\ spaces.ts")
		})

		it("should handle case-insensitive matching for cwd", () => {
			const absPath = "/users/test/project/src/file with spaces.ts" // Lowercase path
			expect(convertToMentionPath(absPath, MOCK_CWD_POSIX)).toBe("@/src/file\\ with\\ spaces.ts") // Should still match uppercase CWD
			const absPathUpper = "/USERS/TEST/PROJECT/src/file.ts" // Uppercase path
			expect(convertToMentionPath(absPathUpper, MOCK_CWD_POSIX.toLowerCase())).toBe("@/src/file.ts") // Should match lowercase CWD
		})

		it("should return original path if cwd is not provided", () => {
			const absPath = "/Users/<USER>/project/src/file with spaces.ts"
			expect(convertToMentionPath(absPath, undefined)).toBe("/Users/<USER>/project/src/file with spaces.ts")
		})
	})
})
