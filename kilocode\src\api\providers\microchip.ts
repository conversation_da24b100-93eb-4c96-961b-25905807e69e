/**
 * Microchip API Provider for KiloCode
 *
 * This implementation exactly matches the MPLAB AI coding assistant CHATBOTAPI.ts approach:
 * - Always uses "microchip-chatbot-internal" model
 * - Uses { questions: [message] } payload format
 * - Uses "api-key" header
 * - Processes prompts with <|im_start|> prefix
 * - Strips images from messages
 */

import { Anthropic } from "@anthropic-ai/sdk"
import NodeCache from "node-cache"

import type { ProviderSettings, ModelInfo, ApiHandlerCreateMessageMetadata } from "@roo-code/types"
import { microchipDefaultModelId, microchipModels, type MicrochipModelId } from "@roo-code/types"

import { ApiStream } from "../transform/stream"
import { logger } from "../../utils/logging"

import { SingleCompletionHandler } from "../index"
import { BaseProvider } from "./base-provider"

// Cache configuration
const CACHE_TTL = 30 // Cache TTL in minutes
const RESPONSE_CACHE_SIZE = 100 // Maximum number of cached responses

/**
 * Microchip API handler for interacting with the Microchip AI Chatbot service.
 *
 * This handler exactly matches the MPLAB AI coding assistant CHATBOTAPI.ts implementation:
 * - Always uses "microchip-chatbot-internal" model
 * - Uses CodeCompletionLLM endpoint
 * - Uses { questions: [message] } payload format
 * - Direct fetch API usage
 * - Enhanced error handling specific to Microchip API
 * - Response caching for improved performance
 */
export class MicrochipHandler extends BaseProvider implements SingleCompletionHandler {
	protected options: ProviderSettings
	private responseCache: NodeCache
	private tokenCountCache: NodeCache
	private apiUrl: string

	constructor(options: ProviderSettings) {
		super()
		this.options = options

		// Initialize caches
		this.responseCache = new NodeCache({
			stdTTL: CACHE_TTL * 60,
			checkperiod: 60,
			maxKeys: RESPONSE_CACHE_SIZE,
		})
		this.tokenCountCache = new NodeCache({
			stdTTL: CACHE_TTL * 60,
			checkperiod: 60,
		})

		const apiKey = this.options.microchipApiKey ?? "not-provided"
		if (apiKey === "not-provided") {
			logger.warn("Microchip API key not provided", {
				ctx: "microchip",
			})
		}

		// Use the CHATBOTAPI endpoint like MPLAB AI
		this.apiUrl =
			this.options.microchipBaseUrl ??
			"https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM"

		logger.info("Initializing Microchip API client", {
			ctx: "microchip",
			apiUrl: this.apiUrl,
		})

		logger.info("Using CHATBOTAPI approach matching MPLAB AI implementation", {
			ctx: "microchip",
		})
	}

	/**
	 * Creates a message stream from the Microchip API
	 *
	 * This implementation exactly matches the MPLAB AI coding assistant CHATBOTAPI.ts approach:
	 * - Uses { questions: [processedMessages] } payload format
	 * - Strips images from messages
	 * - Ensures prompts start with <|im_start|> prefix
	 *
	 * @param systemPrompt The system prompt to use
	 * @param messages The messages to send to the API
	 * @param cacheKey Optional cache key for prompt caching
	 * @returns A stream of response chunks
	 */
	override async *createMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		metadata?: ApiHandlerCreateMessageMetadata,
	): ApiStream {
		const { id: model, info } = this.getModel()
		const cacheKey = metadata?.cacheKey

		try {
			// Convert messages to text format and strip images like MPLAB AI
			const processedMessages = this.convertMessagesToText(systemPrompt, messages)

			// Check cache first
			if (cacheKey) {
				const cachedResponse = this.responseCache.get<string>(cacheKey)
				if (cachedResponse) {
					logger.info("Returning cached response for Microchip API", {
						ctx: "microchip",
						cacheKey,
					})

					// Estimate token counts for usage metrics
					const inputTokens = await this.estimateInputTokens(systemPrompt, messages)
					const outputTokens = await this.estimateOutputTokens(cachedResponse)

					// Yield the cached response as chunks
					const chunks = cachedResponse.split(" ")
					for (const chunk of chunks) {
						yield {
							type: "text",
							text: chunk + " ",
						}
					}

					// Add usage information
					yield {
						type: "usage",
						inputTokens,
						outputTokens,
					}

					return
				}
			}

			// Create the payload using CHATBOTAPI format
			const conversation = {
				questions: [processedMessages],
			}

			logger.info("Sending request to Microchip API", {
				ctx: "microchip",
				model,
				payload: conversation,
			})

			// Make the request using fetch with custom headers
			const response = await fetch(this.apiUrl, {
				method: "POST",
				headers: this.getHeaders(),
				body: JSON.stringify(conversation),
			})

			if (!response.ok) {
				throw new Error(
					`Failed to connect to Microchip Chatbot 😔. HTTP error. Status: ${response.status}`,
				)
			}

			// Handle response using CHATBOTAPI approach - stream response
			let fullResponse = ""
			let hasReceivedContent = false

			const reader = response.body?.getReader()
			if (!reader) {
				throw new Error("Failed to read response from Microchip Chatbot 😕, please try again.")
			}

			// Stream the response exactly like MPLAB AI
			while (true) {
				const { value, done } = await reader.read()
				if (value) {
					const chunk = new TextDecoder().decode(value)
					hasReceivedContent = true
					fullResponse += chunk
					yield {
						type: "text",
						text: chunk,
					}
				}
				if (done) {
					break
				}
			}

			if (!hasReceivedContent) {
				throw new Error("The Microchip API did not provide any assistant messages in the response")
			}

			// Cache the response if we have a cache key
			if (cacheKey && fullResponse) {
				this.responseCache.set(cacheKey, fullResponse)
				logger.info("Cached response for Microchip API", {
					ctx: "microchip",
					cacheKey,
					responseLength: fullResponse.length,
				})
			}

			// Estimate token counts for usage metrics
			const inputTokens = await this.estimateInputTokens(systemPrompt, messages)
			const outputTokens = await this.estimateOutputTokens(fullResponse)

			// Add usage information at the end
			yield {
				type: "usage",
				inputTokens,
				outputTokens,
			}
		} catch (error: any) {
			// Handle the error like MPLAB AI
			logger.error("Microchip API error in createMessage", { ctx: "microchip", error })
			throw new Error("There was an issue contacting the server 😓 Please try again later.")
		}
	}

	/**
	 * Gets the headers for Microchip API requests
	 * Uses CHATBOTAPI approach with api-key header
	 */
	private getHeaders(): Record<string, string> {
		return {
			"Content-Type": "application/json",
			"api-key": this.options.microchipApiKey ?? "",
		}
	}

	/**
	 * Converts Anthropic messages to text format for Microchip API
	 * Strips images and ensures proper formatting like MPLAB AI
	 */
	private convertMessagesToText(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): string {
		let result = systemPrompt ? `${systemPrompt}\n\n` : ""

		for (const message of messages) {
			const role = message.role === "assistant" ? "Assistant" : "User"
			let content = ""

			if (typeof message.content === "string") {
				content = message.content
			} else if (Array.isArray(message.content)) {
				// Strip images and only keep text content like MPLAB AI
				content = message.content
					.filter((block) => block.type === "text")
					.map((block) => (block as any).text)
					.join("")
			}

			result += `${role}: ${content}\n\n`
		}

		// Ensure prompt starts with <|im_start|> like MPLAB AI
		return this.ensurePromptStartsWithImStart(result)
	}

	/**
	 * Ensures the prompt starts with <|im_start|> prefix like MPLAB AI
	 */
	private ensurePromptStartsWithImStart(prompt: string): string {
		if (!prompt.startsWith("<|im_start|>")) {
			return `<|im_start|>${prompt}`
		}
		return prompt
	}

	/**
	 * Estimates input token count for usage metrics
	 */
	private async estimateInputTokens(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): Promise<number> {
		const cacheKey = `input_tokens_${systemPrompt.length}_${messages.length}`
		const cached = this.tokenCountCache.get<number>(cacheKey)
		if (cached) {
			return cached
		}

		// Simple estimation: ~4 characters per token
		const totalText = systemPrompt + messages.map((m) => JSON.stringify(m.content)).join("")
		const estimated = Math.ceil(totalText.length / 4)

		this.tokenCountCache.set(cacheKey, estimated)
		return estimated
	}

	/**
	 * Estimates output token count for usage metrics
	 */
	private async estimateOutputTokens(text: string): Promise<number> {
		const cacheKey = `output_tokens_${text.length}`
		const cached = this.tokenCountCache.get<number>(cacheKey)
		if (cached) {
			return cached
		}

		// Simple estimation: ~4 characters per token
		const estimated = Math.ceil(text.length / 4)

		this.tokenCountCache.set(cacheKey, estimated)
		return estimated
	}

	/**
	 * Completes a prompt using the Microchip API
	 *
	 * This implementation follows the MPLAB AI coding assistant CHATBOTAPI approach.
	 * Features:
	 * - Response caching for improved performance
	 * - Enhanced error handling
	 * - Uses CHATBOTAPI payload format
	 * - Ensures prompt starts with <|im_start|> prefix
	 *
	 * @param prompt The prompt to complete
	 * @returns The completed text
	 */
	async completePrompt(prompt: string): Promise<string> {
		const { id: modelId, info } = this.getModel()

		try {
			// Check cache first
			const cacheKey = `complete_${prompt.slice(0, 100)}`
			const cachedResponse = this.responseCache.get<string>(cacheKey)
			if (cachedResponse) {
				logger.info("Returning cached completion for Microchip API", {
					ctx: "microchip",
					cacheKey,
				})
				return cachedResponse
			}

			// Ensure prompt starts with <|im_start|> like MPLAB AI
			const processedPrompt = this.ensurePromptStartsWithImStart(prompt)

			// Create the payload using CHATBOTAPI format
			const conversation = {
				questions: [processedPrompt],
			}

			logger.info("Sending completePrompt request to Microchip API", {
				ctx: "microchip",
				model: modelId,
				payload: conversation,
			})

			// Make the request using fetch
			const response = await fetch(this.apiUrl, {
				method: "POST",
				headers: this.getHeaders(),
				body: JSON.stringify(conversation),
			})

			if (!response.ok) {
				throw new Error(
					`Failed to connect to Microchip Chatbot 😔. HTTP error. Status: ${response.status}`,
				)
			}

			// Read the full response
			const completionText = await response.text()

			if (!completionText) {
				throw new Error("The Microchip API did not provide any completion text")
			}

			// Cache the response
			this.responseCache.set(cacheKey, completionText)
			logger.info("Cached completion for Microchip API", {
				ctx: "microchip",
				cacheKey,
				responseLength: completionText.length,
			})

			return completionText
		} catch (error: any) {
			// Handle the error like MPLAB AI
			logger.error("Microchip API error in completePrompt", { ctx: "microchip", error })
			throw new Error("There was an issue contacting the server 😓 Please try again later.")
		}
	}

	/**
	 * Gets the model information for the current configuration
	 * Always returns "microchip-chatbot-internal" to match MPLAB AI implementation
	 * @returns The model ID and info
	 */
	override getModel(): { id: string; info: ModelInfo } {
		// Always use microchip-chatbot-internal model like MPLAB AI
		const modelId = "microchip-chatbot-internal"
		const info = microchipModels[modelId as MicrochipModelId] || microchipModels[microchipDefaultModelId]

		return { id: modelId, info }
	}
}
