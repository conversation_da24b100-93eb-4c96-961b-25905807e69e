/**
 * Use `Developer: Generate Color Theme From Current Settings` to generate themes
 * using your current VSCode settings.
 *
 * See: https://code.visualstudio.com/docs/getstarted/themes
 */

:root {
	/**
	 * The styles below have been defined for Tailwind.
	 */

	--vscode-foreground: #d1d5da; /* "foreground" */

	--vscode-editor-foreground: #e1e4e8; /* "editor.foreground" */
	--vscode-editor-background: #24292e; /* "editor.background" */

	--vscode-button-foreground: #dcffe4; /* "button.foreground" */
	--vscode-button-background: #176f2c; /* "button.background" */
	--vscode-button-secondaryForeground: #ffffff; /* "button.secondaryForeground" */
	--vscode-button-secondaryBackground: #444d56; /* "button.secondaryBackground" */

	--vscode-dropdown-foreground: #e1e4e8; /* "dropdown.foreground" */
	--vscode-dropdown-background: #2f363d; /* "dropdown.background" */
	--vscode-dropdown-border: #1b1f23; /* "dropdown.border" */

	--vscode-input-foreground: #e1e4e8; /* "input.foreground" */
	--vscode-input-background: #2f363d; /* "input.background" */
	--vscode-input-border: #1b1f23; /* "input.border" */

	--vscode-focusBorder: #005cc5; /* "focusBorder" */

	--vscode-badge-foreground: #c8e1ff; /* "badge.foreground" */
	--vscode-badge-background: #044289; /* "badge.background" */

	--vscode-notifications-foreground: #e1e4e8; /* "notifications.foreground" */
	--vscode-notifications-background: #2f363d; /* "notifications.background" */
	--vscode-notifications-border: #1b1f23; /* "notifications.border" */

	--vscode-descriptionForeground: #959da5; /* "descriptionForeground" */
	--vscode-errorForeground: #f97583; /* "errorForeground" */

	--vscode-list-hoverForeground: #e1e4e8; /* "list.hoverForeground" */
	--vscode-list-hoverBackground: #282e34; /* "list.hoverBackground" */
	--vscode-list-focusBackground: #044289; /* "list.focusBackground" */

	/**
	 * The styles below are used in the @shadcn/ui + Tailwind theme but are
	 * not exposed as `--color-vscode-*` variables.
	 */

	--vscode-menu-background: #2f363d; /* "menu.background" [MISSING] */
	--vscode-menu-foreground: #e1e4e8; /* "menu.foreground" [MISSING] */

	--vscode-disabledForeground: #cccccc; /* "disabledForeground" [MISSING] */
}
