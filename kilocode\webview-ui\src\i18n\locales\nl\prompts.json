{"title": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "modes": {"title": "<PERSON><PERSON>", "createNewMode": "Nieuwe modus aanmaken", "editModesConfig": "Modusconfiguratie bewerken", "editGlobalModes": "Globale modi bewerken", "editProjectModes": "Projectmodi bewerken (.kilocodemodes)", "createModeHelpText": "<PERSON><PERSON> zijn gespecialiseerde persona's die het gedrag van Kilo Code aanpassen. <0><PERSON><PERSON> informatie over het gebru<PERSON> van modi</0> of <1>het aanpassen van modi.</1>", "selectMode": "Modus z<PERSON>"}, "apiConfiguration": {"title": "API-configuratie", "select": "Selecteer welke API-configuratie voor deze modus gebruikt moet worden"}, "tools": {"title": "Beschikbare tools", "builtInModesText": "Tools voor ingebouwde modi kunnen niet worden aangepast", "editTools": "Tools bewerken", "doneEditing": "Bewerken voltooid", "allowedFiles": "<PERSON><PERSON><PERSON><PERSON> best<PERSON>:", "toolNames": {"read": "<PERSON><PERSON><PERSON> le<PERSON>", "edit": "Bestanden bewerken", "browser": "Browser geb<PERSON>iken", "command": "Commando's u<PERSON><PERSON><PERSON><PERSON>", "mcp": "MCP gebruiken"}, "noTools": "<PERSON><PERSON>"}, "roleDefinition": {"title": "Roldefinitie", "resetToDefault": "Terugzetten naar standaard", "description": "<PERSON><PERSON><PERSON><PERSON>'s expertise en persoonlijkheid voor deze modus. Deze beschrijving bepaalt hoe Ki<PERSON> zich presenteert en taken benadert."}, "whenToUse": {"title": "<PERSON><PERSON> te geb<PERSON>ike<PERSON> (optioneel)", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> wanneer deze modus gebruikt moet worden. <PERSON><PERSON> helpt de Orchestrator om de juiste modus voor een taak te kiezen.", "resetToDefault": "Beschrijving '<PERSON><PERSON> te gebruiken' terugzetten naar standaard"}, "customInstructions": {"title": "Modusspecifieke instructies (optioneel)", "resetToDefault": "Terugzetten naar standaard", "description": "Voeg gedragsrichtlijnen toe die specifiek zijn voor de modus {{modeName}}.", "loadFromFile": "Modusspecifieke instructies voor {{mode}} kunnen ook worden geladen uit de map <span>.kilocode/rules-{{slug}}/</span> in je werkruimte (.kilocoderules-{{slug}} en .clinerules-{{slug}} zijn verouderd en werken binnenkort niet meer)."}, "globalCustomInstructions": {"title": "Aangepaste instructies voor alle modi", "description": "Deze instructies gelden voor alle modi. Ze bieden een basisset aan gedragingen die kunnen worden uitgebreid met modusspecifieke instructies hieronder. <0>Meer informatie</0>", "loadFromFile": "Instructies kunnen ook worden geladen uit de map <span>.kilocode/rules/</span> in je werkruimte (.kilocoderules en .clinerules zijn verouderd en werken binnenkort niet meer)."}, "systemPrompt": {"preview": "Systeemp<PERSON><PERSON> be<PERSON>en", "copy": "Systeemprompt kopiëren naar klembord", "title": "Systeemprompt ({{modeName}} modus)"}, "supportPrompts": {"title": "Ondersteuningsprompts", "resetPrompt": "Reset {{promptType}} prompt naar standaard", "prompt": "Prompt", "enhance": {"apiConfiguration": "API-configuratie", "apiConfigDescription": "Je kunt een API-configuratie selecteren die altijd wordt gebruikt voor het verbeteren van prompts, of gewoon de huidige selectie gebruiken", "useCurrentConfig": "Huidige API-configuratie g<PERSON>n", "testPromptPlaceholder": "<PERSON><PERSON>r een prompt in om de verbetering te testen", "previewButton": "Voorbeeld promptverbetering", "testEnhancement": "Test verbetering"}, "types": {"ENHANCE": {"label": "Prompt verbeteren", "description": "Gebruik promptverbetering om op maat gemaakte suggesties of verbeteringen voor je invoer te krijgen. Zo begrijpt Kilo Code je intentie en krijg je de best mogelijke antwoorden. Beschikbaar via het ✨-icoon in de chat."}, "EXPLAIN": {"label": "Code uitleggen", "description": "<PERSON><PERSON><PERSON><PERSON> gedetailleerde uitleg over codefragmenten, functies of hele bestanden. Handig om complexe code te begrijpen of nieuwe patronen te leren. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "FIX": {"label": "Problemen oplossen", "description": "Krijg hulp bij het identificeren en oplossen van bugs, fouten of codekwaliteitsproblemen. Biedt stapsgewijze begeleiding bij het oplossen van problemen. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "IMPROVE": {"label": "Code verbeteren", "description": "Ontvang suggesties voor codeoptimalisatie, betere praktijken en architecturale verbeteringen met behoud van functionaliteit. <PERSON>sch<PERSON><PERSON>ar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "ADD_TO_CONTEXT": {"label": "Aan context toevoegen", "description": "Voeg context toe aan je huidige taak of gesprek. Handig voor extra informatie of verduidelijkingen. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Terminalinhoud aan context toevoegen", "description": "Voeg terminaluitvoer toe aan je huidige taak of gesprek. Handig voor commando-uitvoer of logboeken. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "TERMINAL_FIX": {"label": "Terminalcommando repareren", "description": "<PERSON><PERSON><PERSON><PERSON> hulp bij het repareren van terminalcommando's die zijn mislukt of verbetering nodig hebben. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "TERMINAL_EXPLAIN": {"label": "Terminalcommando uitleggen", "description": "K<PERSON><PERSON>g gedetailleerde uitleg over terminalcommando's en hun uitvoer. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "NEW_TASK": {"label": "<PERSON><PERSON><PERSON> taak starten", "description": "Start een ni<PERSON><PERSON> met geb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> via de Command <PERSON>."}}}, "advancedSystemPrompt": {"title": "Geavanceerd: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<2>⚠️ Waarschuwing:</2> Deze geavanceerde functie omzeilt beveiligingen. <1>LEES DIT VOOR GEBRUIK!</1>Overschrijf de standaard systeemprompt door een bestand aan te maken op <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Nieuwe modus aanmaken", "close": "Sluiten", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> de na<PERSON> van de modus in"}, "slug": {"label": "Slug", "description": "De slug wordt gebruikt in URL's en bestandsnamen. Moet kleine letters, cijfers en koppeltekens bevatten."}, "saveLocation": {"label": "Opslaglocatie", "description": "<PERSON><PERSON> waar je deze modus wilt opslaan. Projectspecifieke modi hebben voorrang op globale modi.", "global": {"label": "Globaal", "description": "Beschikbaar in alle werkruimtes"}, "project": {"label": "Projectspecifiek (.kilocodemodes)", "description": "<PERSON><PERSON> be<PERSON> in deze werkruimte, heeft voorrang op globaal"}}, "roleDefinition": {"label": "Roldefinitie", "description": "<PERSON><PERSON><PERSON><PERSON>'s expertise en persoonlijkheid voor deze modus."}, "whenToUse": {"label": "<PERSON><PERSON> te geb<PERSON>ike<PERSON> (optioneel)", "description": "<PERSON><PERSON> een duidelijke beschrijving van wanneer deze modus het meest effectief is en voor welke soorten taken deze uitblinkt."}, "tools": {"label": "Beschikbare tools", "description": "Selecteer welke tools deze modus kan gebruiken."}, "customInstructions": {"label": "Aangepaste instructies (optioneel)", "description": "Voeg gedragsrichtlijnen toe die specifiek zijn voor deze modus."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "Modus aanmaken"}, "deleteMode": "Modus verwi<PERSON>en"}, "allFiles": "alle bestanden"}