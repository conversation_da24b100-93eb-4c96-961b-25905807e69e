{"greeting": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>!", "introduction": "<strong>Kilo Code to wiodący autonomiczny agent kodowania.</strong> Przygotuj się na projektowanie architektury, kodowanie, debugowanie i zwiększenie produktywności jak nigdy dotąd. <PERSON><PERSON>, Kilo Code wymaga klucza API.", "notice": "<PERSON><PERSON>, to rozszerzenie potrzebuje dostawcy API.", "start": "Zaczynajmy!", "chooseProvider": "<PERSON><PERSON><PERSON><PERSON>w<PERSON>, aby r<PERSON><PERSON>:", "routers": {"requesty": {"description": "Twój zoptymalizowany router LLM", "incentive": "$1 darmowego kredytu"}, "openrouter": {"description": "Ujednolicony interfejs dla LLMs"}}, "startRouter": "Szybka konfiguracja przez router", "startCustom": "Użyj własnego klucza API", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON> Kilo <PERSON>", "anonymousTelemetry": "Wyślij anonimowe dane o błędach i użyciu, aby pomóc nam w naprawianiu błędów i ulepszaniu rozszerzenia. Nigdy nie są wysyłane żadne kody, teksty ani informacje osobiste.", "changeSettings": "Zawsze możesz to zmienić na dole <settingsLink>ustawień</settingsLink>", "settings": "ustawienia", "allow": "Zezwól", "deny": "Odmów"}, "or": "lub", "importSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}