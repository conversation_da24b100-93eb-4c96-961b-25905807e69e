{"greeting": "こんにちは、Kilo Codeです！", "introduction": "<strong>Kilo Codeは最高の自律型コーディングエージェントです。</strong>アーキテクチャの設計、コーディング、デバッグ、そして今までにない生産性向上を体験する準備をしてください。続行するには、Kilo CodeにはAPIキーが必要です。", "notice": "開始するには、この拡張機能にはAPIプロバイダーが必要です。", "start": "さあ、始めましょう！", "chooseProvider": "開始するにはAPIプロバイダーを選択してください：", "routers": {"requesty": {"description": "最適化されたLLMルーター", "incentive": "$1の無料クレジット"}, "openrouter": {"description": "LLMsのための統一インターフェース"}}, "startRouter": "ルーター経由の簡単セットアップ", "startCustom": "自分のAPIキーを使用", "telemetry": {"title": "Kilo Codeの改善にご協力ください", "anonymousTelemetry": "バグの修正と拡張機能の改善のため、匿名のエラーと使用データを送信してください。コード、プロンプト、個人情報は一切送信されません。", "changeSettings": "<settingsLink>設定</settingsLink>の下部でいつでも変更できます", "settings": "設定", "allow": "許可", "deny": "拒否"}, "or": "または", "importSettings": "設定をインポート"}