{"greeting": "嗨，我是 Kilo Code！", "introduction": "<strong>Kilo Code 是頂尖的自主編程助手。</strong>準備好以前所未有的方式進行架構設計、編碼、除錯並提升您的工作效率。要繼續使用，Kilo Code 需要一個 API 金鑰。", "notice": "開始使用前，此擴充功能需要一個 API 提供者。", "start": "讓我們開始吧！", "chooseProvider": "選擇一個 API 提供者開始：", "routers": {"requesty": {"description": "您的最佳化 LLM 路由器", "incentive": "$1 免費額度"}, "openrouter": {"description": "LLM 的統一介面"}}, "startRouter": "透過路由器快速設定", "startCustom": "使用您自己的 API 金鑰", "telemetry": {"title": "協助改進 Kilo Code", "anonymousTelemetry": "傳送匿名的錯誤和使用資料，以協助我們修復錯誤並改進擴充功能。我們絕不會傳送任何程式碼、提示或個人資訊。", "changeSettings": "您隨時可以在<settingsLink>設定</settingsLink>底端更改此選項", "settings": "設定", "allow": "允許", "deny": "拒絕"}, "or": "或是", "importSettings": "匯入設定"}