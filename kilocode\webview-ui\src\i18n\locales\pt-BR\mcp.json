{"title": "Servidores MCP", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "O <0>Model Context Protocol</0> permite a comunicação com servidores MCP em execução localmente que fornecem ferramentas e recursos adicionais para estender as capacidades do Kilo Code. Você pode usar <1>servidores criados pela comunidade</1> ou pedir ao Kilo Code para criar novas ferramentas específicas para seu fluxo de trabalho (por exemplo, \"adicionar uma ferramenta que obtém a documentação mais recente do npm\").", "instructions": "Instruções", "enableToggle": {"title": "Ativar servidores MCP", "description": "Ative para que o Kilo Code possa usar ferramentas de servidores MCP conectados. Isso dá mais capacidades ao Kilo Code. Se você não pretende usar essas ferramentas extras, desative para ajudar a reduzir os custos de tokens da API."}, "enableServerCreation": {"title": "Ativar criação de servidores MCP", "description": "Ative para que o Kilo Code possa te ajudar a criar <1>novos</1> servidores MCP personalizados. <0>Saiba mais sobre criação de servidores</0>", "hint": "Dica: Para reduzir os custos de tokens da API, desative esta configuração quando não estiver pedindo ao Kilo Code para criar um novo servidor MCP."}, "editGlobalMCP": "Editar MCP global", "editProjectMCP": "Editar MCP do projeto", "learnMoreEditingSettings": "Saiba mais sobre como editar arquivos de configuração MCP", "tool": {"alwaysAllow": "Sempre permitir", "parameters": "Parâmetros", "noDescription": "Sem descrição"}, "tabs": {"tools": "Ferramentas", "resources": "Recursos", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON>enhuma ferramenta encontrada", "noResources": "Nenhum recurso encontrado", "noLogs": "Nenhum log encontrado", "noErrors": "Nenhum erro encontrado"}, "networkTimeout": {"label": "Tempo limite de rede", "description": "Tempo máximo de espera por respostas do servidor", "options": {"15seconds": "15 segundos", "30seconds": "30 segundos", "1minute": "1 minuto", "5minutes": "5 minutos", "10minutes": "10 minutos", "15minutes": "15 minutos", "30minutes": "30 minutos", "60minutes": "60 minutos"}}, "deleteDialog": {"title": "Excluir servidor MCP", "description": "Tem certeza de que deseja excluir o servidor MCP \"{{serverName}}\"? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir"}, "serverStatus": {"retrying": "Tentando novamente...", "retryConnection": "Tentar reconectar"}}