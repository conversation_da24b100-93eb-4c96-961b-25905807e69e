{"greeting": "Kilo Code 能為您做什麼？", "task": {"title": "工作", "seeMore": "顯示更多", "seeLess": "顯示較少", "tokens": "Tokens:", "cache": "快取：", "apiCost": "API 費用：", "contextWindow": "上下文長度：", "closeAndStart": "關閉現有工作並開始一項新的工作", "export": "匯出工作紀錄", "delete": "刪除工作（按住 Shift 並點選可跳過確認）", "condenseContext": "智慧壓縮上下文"}, "history": {"title": "歷史"}, "unpin": "取消置頂", "pin": "置頂", "tokenProgress": {"availableSpace": "可用空間：{{amount}} tokens", "tokensUsed": "已使用 tokens: {{used}} / {{total}}", "reservedForResponse": "為模型回應保留：{{amount}} tokens"}, "retry": {"title": "重試", "tooltip": "再次嘗試操作"}, "startNewTask": {"title": "開始新任務", "tooltip": "開始一個新任務"}, "reportBug": {"title": "回報錯誤"}, "proceedAnyways": {"title": "仍然繼續", "tooltip": "在指令執行時繼續"}, "save": {"title": "儲存", "tooltip": "儲存檔案變更"}, "reject": {"title": "拒絕", "tooltip": "拒絕此操作"}, "completeSubtaskAndReturn": "完成子工作並返回", "approve": {"title": "核准", "tooltip": "核准此操作"}, "runCommand": {"title": "執行命令", "tooltip": "執行此命令"}, "proceedWhileRunning": {"title": "執行時繼續", "tooltip": "儘管有警告仍繼續執行"}, "killCommand": {"title": "終止指令", "tooltip": "終止目前的指令"}, "resumeTask": {"title": "繼續工作", "tooltip": "繼續目前的工作"}, "terminate": {"title": "終止", "tooltip": "結束目前的工作"}, "cancel": {"title": "取消", "tooltip": "取消目前操作"}, "scrollToBottom": "捲動至對話框底部", "about": "透過 AI 輔助產生、重構和偵錯程式碼。查看我們的 <DocsLink>說明文件</DocsLink> 以瞭解更多資訊。", "onboarding": "<strong>您在此工作區中的工作清單是空的。</strong> 請在下方輸入工作以開始。 不確定如何開始？ 在 <DocsLink>說明文件</DocsLink> 中閱讀更多關於 Kilo Code 能為您做什麼的資訊。", "rooTips": {"boomerangTasks": {"title": "任務拆分", "description": "將任務拆分為更小、更易於管理的部分。"}, "stickyModels": {"title": "黏性模式", "description": "每個模式都會記住您上次使用的模型"}, "tools": {"title": "工具", "description": "允許 AI 透過瀏覽網路、執行命令等方式解決問題。"}, "customizableModes": {"title": "自訂模式", "description": "具有專屬行為和指定模型的特定角色"}}, "selectMode": "選擇互動模式", "selectApiConfig": "選取 API 設定", "enhancePrompt": "使用額外內容增強提示", "addImages": "新增圖片到訊息中", "sendMessage": "傳送訊息", "typeMessage": "輸入訊息...", "typeTask": "構建、查找、詢問", "addContext": "輸入 @ 新增內容，輸入 / 切換模式", "dragFiles": "按住 Shift 鍵拖曳檔案", "dragFilesImages": "按住 Shift 鍵拖曳檔案/圖片", "enhancePromptDescription": "「增強提示」按鈕透過提供額外內容、說明或重新表述來幫助改進您的請求。嘗試在此處輸入請求，然後再次點選按鈕以了解其運作方式。", "errorReadingFile": "讀取檔案時發生錯誤：", "noValidImages": "未處理到任何有效圖片", "separator": "分隔符號", "edit": "編輯...", "forNextMode": "用於下一個模式", "error": "錯誤", "diffError": {"title": "編輯失敗"}, "troubleMessage": "Kilo Code 遇到問題...", "apiRequest": {"title": "API 請求", "failed": "API 請求失敗", "streaming": "正在處理 API 請求...", "cancelled": "API 請求已取消", "streamingFailed": "API 串流處理失敗"}, "checkpoint": {"initial": "初始檢查點", "regular": "檢查點", "initializingWarning": "正在初始化檢查點...如果耗時過長，你可以在<settingsLink>設定</settingsLink>中停用檢查點並重新啟動任務。", "menu": {"viewDiff": "檢視差異", "restore": "還原檢查點", "restoreFiles": "還原檔案", "restoreFilesDescription": "將您的專案檔案還原到此時的快照。", "restoreFilesAndTask": "還原檔案和工作", "confirm": "確認", "cancel": "取消", "cannotUndo": "此操作無法復原。", "restoreFilesAndTaskDescription": "將您的專案檔案還原到此時的快照，並刪除此點之後的所有訊息。"}, "current": "目前"}, "instructions": {"wantsToFetch": "Kilo Code 想要取得詳細指示以協助目前任務"}, "fileOperations": {"wantsToRead": "Kilo Code 想要讀取此檔案：", "wantsToReadOutsideWorkspace": "Kilo Code 想要讀取此工作區外的檔案：", "didRead": "Kilo Code 已讀取此檔案：", "wantsToEdit": "Kilo Code 想要編輯此檔案：", "wantsToEditOutsideWorkspace": "Kilo Code 想要編輯此工作區外的檔案：", "wantsToCreate": "Kilo Code 想要建立新檔案：", "wantsToSearchReplace": "Kilo Code 想要在此檔案中搜尋和取代：", "didSearchReplace": "Kilo Code 已在此檔案執行搜尋和取代：", "wantsToInsert": "Kilo Code 想要在此檔案中插入內容：", "wantsToInsertWithLineNumber": "Kilo Code 想要在此檔案第 {{lineNumber}} 行插入內容：", "wantsToInsertAtEnd": "Kilo Code 想要在此檔案末尾新增內容：", "wantsToReadAndXMore": "Kilo Code 想要讀取此檔案以及另外 {{count}} 個檔案：", "wantsToReadMultiple": "Kilo Code 想要讀取多個檔案："}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code 想要檢視此目錄中最上層的檔案：", "didViewTopLevel": "Kilo Code 已檢視此目錄中最上層的檔案：", "wantsToViewRecursive": "Kilo Code 想要遞迴檢視此目錄中的所有檔案：", "didViewRecursive": "Kilo Code 已遞迴檢視此目錄中的所有檔案：", "wantsToViewDefinitions": "Kilo Code 想要檢視此目錄中使用的原始碼定義名稱：", "didViewDefinitions": "Kilo Code 已檢視此目錄中使用的原始碼定義名稱：", "wantsToSearch": "Kilo Code 想要在此目錄中搜尋 <code>{{regex}}</code>：", "didSearch": "Kilo Code 已在此目錄中搜尋 <code>{{regex}}</code>："}, "commandOutput": "命令輸出", "response": "回應", "arguments": "參數", "mcp": {"wantsToUseTool": "Kilo Code 想要在 {{serverName}} MCP 伺服器上使用工具：", "wantsToAccessResource": "Kilo Code 想要存取 {{serverName}} MCP 伺服器上的資源："}, "modes": {"wantsToSwitch": "Kilo Code 想要切換至 <code>{{mode}}</code> 模式", "wantsToSwitchWithReason": "Kilo Code 想要切換至 <code>{{mode}}</code> 模式，原因：{{reason}}", "didSwitch": "Kilo Code 已切換至 <code>{{mode}}</code> 模式", "didSwitchWithReason": "Kilo Code 已切換至 <code>{{mode}}</code> 模式，原因：{{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code 想要在 <code>{{mode}}</code> 模式下建立新的子工作：", "wantsToFinish": "Kilo Code 想要完成此子工作", "newTaskContent": "子工作指示", "completionContent": "子工作已完成", "resultContent": "子工作結果", "defaultResult": "請繼續下一個工作。", "completionInstructions": "子工作已完成！您可以檢閱結果並提出修正或下一步建議。如果一切看起來良好，請確認以將結果傳回主工作。"}, "questions": {"hasQuestion": "Kilo Code 有一個問題："}, "taskCompleted": "工作完成", "powershell": {"issues": "看起來您遇到了 Windows PowerShell 的問題，請參考此處"}, "autoApprove": {"title": "自動核准：", "none": "無", "description": "自動核准讓 Kilo Code 可以在無需徵求您同意的情況下執行動作。請僅對您完全信任的動作啟用此功能。您可以在<settingsLink>設定</settingsLink>中進行更詳細的調整。"}, "reasoning": {"thinking": "思考中", "seconds": "{{count}}秒"}, "contextCondense": {"title": "上下文已壓縮", "condensing": "正在壓縮上下文...", "errorHeader": "上下文壓縮失敗", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "複製到輸入框（或按住 Shift 並點選）"}, "announcement": {"title": "🎉 Roo Code {{version}} 已發布", "description": "Roo Code {{version}} 帶來基於您意見回饋的強大新功能與改進。", "whatsNew": "新功能", "feature1": "<bold>智慧上下文壓縮預設啟用</bold>: 上下文壓縮現已預設啟用，並提供可設定的自動壓縮觸發設定", "feature2": "<bold>手動壓縮按鈕</bold>: 工作標題中的新按鈕讓您隨時手動觸發上下文壓縮", "feature3": "<bold>進階壓縮設定</bold>: 透過<contextSettingsLink>上下文設定</contextSettingsLink>精確控制自動壓縮的時機和方式", "hideButton": "隱藏公告", "detailsDiscussLinks": "在 <discordLink>Discord</discordLink> 和 <redditLink>Reddit</redditLink> 取得更多詳細資訊並參與討論 🚀"}, "browser": {"rooWantsToUse": "Kilo Code 想要使用瀏覽器：", "consoleLogs": "主控台記錄", "noNewLogs": "（沒有新記錄）", "screenshot": "瀏覽器螢幕擷圖", "cursor": "游標", "navigation": {"step": "步驟 {{current}} / {{total}}", "previous": "上一步", "next": "下一步"}, "sessionStarted": "瀏覽器工作階段已啟動", "actions": {"title": "瀏覽器動作：", "launch": "在 {{url}} 啟動瀏覽器", "click": "點選 ({{coordinate}})", "type": "輸入「{{text}}」", "scrollDown": "向下捲動", "scrollUp": "向上捲動", "close": "關閉瀏覽器"}}, "codeblock": {"tooltips": {"expand": "展開程式碼區塊", "collapse": "摺疊程式碼區塊", "enable_wrap": "啟用自動換行", "disable_wrap": "停用自動換行", "copy_code": "複製程式碼"}}, "systemPromptWarning": "警告：自訂系統提示詞覆蓋已啟用。這可能嚴重破壞功能並導致不可預測的行為。", "profileViolationWarning": "目前設定檔違反了您的組織設定", "shellIntegration": {"title": "命令執行警告", "description": "您的命令正在沒有 VSCode 終端機 shell 整合的情況下執行。要隱藏此警告，您可以在 <settingsLink>Kilo Code 設定</settingsLink>的 <strong>Terminal</strong> 部分停用 shell 整合，或使用下方連結排查 VSCode 終端機整合問題。", "troubleshooting": "點擊此處查看 shell 整合文件。"}, "ask": {"autoApprovedRequestLimitReached": {"title": "已達自動核准請求限制", "description": "Kilo Code 已達到 {{count}} 次 API 請求的自動核准限制。您想要重設計數並繼續工作嗎？", "button": "重設並繼續"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code 想要搜尋程式碼庫：<code>{{query}}</code>", "wantsToSearchWithPath": "Kilo Code 想要在 <code>{{path}}</code> 中搜尋：<code>{{query}}</code>", "didSearch": "找到 {{count}} 個結果：<code>{{query}}</code>"}, "read-batch": {"approve": {"title": "全部核准"}, "deny": {"title": "全部拒絕"}}}