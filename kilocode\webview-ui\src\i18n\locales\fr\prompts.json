{"title": "Modes", "done": "<PERSON><PERSON><PERSON><PERSON>", "modes": {"title": "Modes", "createNewMode": "Créer un nouveau mode", "editModesConfig": "Modifier la configuration des modes", "editGlobalModes": "Modifier les modes globaux", "editProjectModes": "Modifier les modes du projet (.kilocodemodes)", "createModeHelpText": "Les modes sont des personas spécialisés qui adaptent le comportement de Kilo Code. <0>En savoir plus sur l'utilisation des modes</0> ou <1>la personnalisation des modes.</1>", "selectMode": "Rechercher les modes"}, "apiConfiguration": {"title": "Configuration API", "select": "Sélectionnez la configuration API à utiliser pour ce mode"}, "tools": {"title": "Outils disponibles", "builtInModesText": "Les outils pour les modes intégrés ne peuvent pas être modifiés", "editTools": "Modifier les outils", "doneEditing": "Terminer <PERSON>", "allowedFiles": "Fichiers autorisés :", "toolNames": {"read": "<PERSON>re les fichiers", "edit": "Modifier les fichiers", "browser": "Utiliser le navigateur", "command": "Exécuter des commandes", "mcp": "Utiliser MCP"}, "noTools": "Aucun"}, "roleDefinition": {"title": "Définition du rôle", "resetToDefault": "Réinitialiser aux valeurs par défaut", "description": "Définissez l'expertise et la personnalité de Kilo Code pour ce mode. Cette description façonne la manière dont Kilo Code se présente et aborde les tâches."}, "whenToUse": {"title": "Quand utiliser (optionnel)", "description": "Décrivez quand ce mode doit être utilisé. <PERSON><PERSON> aide l'Orchestrateur à choisir le mode approprié pour une tâche.", "resetToDefault": "Réinitialiser la description 'Quand utiliser' aux valeurs par défaut"}, "customInstructions": {"title": "Instructions personnalisées spécifiques au mode (optionnel)", "resetToDefault": "Réinitialiser aux valeurs par défaut", "description": "Ajoutez des directives comportementales spécifiques au mode {{modeName}}.", "loadFromFile": "Les instructions personnalisées spécifiques au mode {{mode}} peuvent également être chargées depuis le dossier <span>.kilocode/rules/</span> dans votre espace de travail (.kilocoderules-{{slug}} est obsolète et cessera de fonctionner bientôt)."}, "globalCustomInstructions": {"title": "Instructions personnalisées pour tous les modes", "description": "Ces instructions s'appliquent à tous les modes. Elles fournissent un ensemble de comportements de base qui peuvent être améliorés par des instructions spécifiques au mode ci-dessous. <0>En savoir plus</0>", "loadFromFile": "Les instructions peuvent également être chargées depuis le dossier <span>.kilocode/rules/</span> dans votre espace de travail (.kilocoderules et .clinerules sont obsolètes et cesseront de fonctionner bientôt)."}, "systemPrompt": {"preview": "Aperçu du prompt système", "copy": "<PERSON><PERSON><PERSON> le prompt système dans le presse-papiers", "title": "Prompt système (mode {{modeName}})"}, "supportPrompts": {"title": "Prompts de support", "resetPrompt": "Réinitialiser le prompt {{promptType}} aux valeurs par défaut", "prompt": "Prompt", "enhance": {"apiConfiguration": "Configuration API", "apiConfigDescription": "Vous pouvez sélectionner une configuration API à toujours utiliser pour améliorer les prompts, ou simplement utiliser celle qui est actuellement sélectionnée", "useCurrentConfig": "Utiliser la configuration API actuellement sélectionnée", "testPromptPlaceholder": "Entrez un prompt pour tester l'amélioration", "previewButton": "Aperçu de l'amélioration du prompt", "testEnhancement": "Tester l'amélioration"}, "types": {"ENHANCE": {"label": "Amé<PERSON><PERSON> le prompt", "description": "Utilisez l'amélioration de prompt pour obtenir des suggestions ou des améliorations personnalisées pour vos entrées. <PERSON><PERSON> garantit que Kilo Code comprend votre intention et fournit les meilleures réponses possibles. Disponible via l'icône ✨ dans le chat."}, "EXPLAIN": {"label": "Expliquer le code", "description": "Obtenez des explications détaillées sur des extraits de code, des fonctions ou des fichiers entiers. Utile pour comprendre un code complexe ou apprendre de nouveaux modèles. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "FIX": {"label": "Corriger les problèmes", "description": "Obtenez de l'aide pour identifier et résoudre les bugs, les erreurs ou les problèmes de qualité du code. Fournit des conseils étape par étape pour résoudre les problèmes. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "IMPROVE": {"label": "Améliorer le code", "description": "Recevez des suggestions pour l'optimisation du code, de meilleures pratiques et des améliorations architecturales tout en maintenant la fonctionnalité. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "ADD_TO_CONTEXT": {"label": "A<PERSON>ter au contexte", "description": "Ajoutez du contexte à votre tâche ou conversation actuelle. Utile pour fournir des informations supplémentaires ou des clarifications. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Ajouter le contenu du terminal au contexte", "description": "Ajoutez la sortie du terminal à votre tâche ou conversation actuelle. Utile pour fournir des sorties de commandes ou des journaux. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "TERMINAL_FIX": {"label": "Corriger la commande du terminal", "description": "Obt<PERSON><PERSON> de l'aide pour corriger les commandes du terminal qui ont échoué ou qui nécessitent des améliorations. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "TERMINAL_EXPLAIN": {"label": "Expliquer la commande du terminal", "description": "Obtenez des explications détaillées sur les commandes du terminal et leurs sorties. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "NEW_TASK": {"label": "<PERSON><PERSON><PERSON><PERSON> une nouvelle tâche", "description": "<PERSON><PERSON><PERSON>re une nouvelle tâche avec ton entrée. Disponible dans la palette de commandes."}}}, "advancedSystemPrompt": {"title": "Avancé : <PERSON><PERSON><PERSON><PERSON> le prompt système", "description": "<2>⚠️ Attention :</2> Cette fonctionnalité avancée contourne les mesures de protection. <1>LISEZ CECI AVANT UTILISATION !</1>Remplacez le prompt système par défaut en créant un fichier à l'emplacement <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Créer un nouveau mode", "close": "<PERSON><PERSON><PERSON>", "name": {"label": "Nom", "placeholder": "Entrez le nom du mode"}, "slug": {"label": "Slug", "description": "Le slug est utilisé dans les URL et les noms de fichiers. Il doit être en minuscules et ne contenir que des lettres, des chiffres et des tirets."}, "saveLocation": {"label": "Emplacement d'enregistrement", "description": "Choisissez où enregistrer ce mode. Les modes spécifiques au projet ont priorité sur les modes globaux.", "global": {"label": "Global", "description": "Disponible dans tous les espaces de travail"}, "project": {"label": "Spécifique au projet (.kilocodemodes)", "description": "Disponible uniquement dans cet espace de travail, a priorité sur le global"}}, "roleDefinition": {"label": "Définition du rôle", "description": "Définissez l'expertise et la personnalité de Kilo Code pour ce mode."}, "whenToUse": {"label": "Quand utiliser (optionnel)", "description": "Fournissez une description claire de quand ce mode est le plus efficace et pour quels types de tâches il excelle."}, "tools": {"label": "Outils disponibles", "description": "Sélectionnez quels outils ce mode peut utiliser."}, "customInstructions": {"label": "Instructions personnalisées (optionnel)", "description": "Ajoutez des directives comportementales spécifiques à ce mode."}, "buttons": {"cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> le mode"}, "deleteMode": "Supprimer le mode"}, "allFiles": "tous les fichiers"}