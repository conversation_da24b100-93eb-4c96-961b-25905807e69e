/* Custom codicon classes */

/**
 * Fixes: https://github.com/microsoft/vscode-codicons/issues/286
 */
.codicon-attach::before {
	content: "";
	display: inline-block;
	width: 1em;
	height: 1em;
	vertical-align: text-top;
	position: relative;
	top: 0em;
	background-color: currentColor;
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'%3E%3Cpath d='M8.25201 15H7.75201C6.8577 14.9646 6.01378 14.5765 5.40476 13.9207C4.79575 13.2648 4.47118 12.3945 4.50201 11.5V3.682C4.47985 2.99536 4.73035 2.32785 5.19878 1.82531C5.66722 1.32278 6.31551 1.02607 7.00201 1C7.68852 1.02607 8.33681 1.32278 8.80524 1.82531C9.27367 2.32785 9.52417 2.99536 9.50201 3.682V10.849C9.51306 11.2586 9.36146 11.6559 9.08037 11.9541C8.79928 12.2522 8.41156 12.4269 8.00201 12.44C7.59635 12.4275 7.21184 12.2561 6.93129 11.9628C6.65073 11.6695 6.49657 11.2778 6.50201 10.872V7H7.50201V10.849C7.49117 10.9934 7.53752 11.1363 7.63105 11.2468C7.72458 11.3574 7.85781 11.4268 8.00201 11.44C8.14622 11.4268 8.27945 11.3574 8.37298 11.2468C8.46651 11.1363 8.51285 10.9934 8.50201 10.849V3.682C8.52443 3.26046 8.37936 2.84714 8.0984 2.53209C7.81744 2.21704 7.42336 2.02579 7.00201 2C6.58067 2.02579 6.18658 2.21704 5.90562 2.53209C5.62466 2.84714 5.47959 3.26046 5.50201 3.682V11.5C5.47146 12.1292 5.69078 12.7451 6.11222 13.2133C6.53365 13.6816 7.12304 13.9643 7.75201 14H8.25201C8.88098 13.9643 9.47038 13.6816 9.89181 13.2133C10.3132 12.7451 10.5326 12.1292 10.502 11.5V5H11.502V11.5C11.5328 12.3945 11.2083 13.2648 10.5993 13.9207C9.99025 14.5765 9.14633 14.9646 8.25201 15V15Z'/%3E%3C/svg%3E");
	mask-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'%3E%3Cpath d='M8.25201 15H7.75201C6.8577 14.9646 6.01378 14.5765 5.40476 13.9207C4.79575 13.2648 4.47118 12.3945 4.50201 11.5V3.682C4.47985 2.99536 4.73035 2.32785 5.19878 1.82531C5.66722 1.32278 6.31551 1.02607 7.00201 1C7.68852 1.02607 8.33681 1.32278 8.80524 1.82531C9.27367 2.32785 9.52417 2.99536 9.50201 3.682V10.849C9.51306 11.2586 9.36146 11.6559 9.08037 11.9541C8.79928 12.2522 8.41156 12.4269 8.00201 12.44C7.59635 12.4275 7.21184 12.2561 6.93129 11.9628C6.65073 11.6695 6.49657 11.2778 6.50201 10.872V7H7.50201V10.849C7.49117 10.9934 7.53752 11.1363 7.63105 11.2468C7.72458 11.3574 7.85781 11.4268 8.00201 11.44C8.14622 11.4268 8.27945 11.3574 8.37298 11.2468C8.46651 11.1363 8.51285 10.9934 8.50201 10.849V3.682C8.52443 3.26046 8.37936 2.84714 8.0984 2.53209C7.81744 2.21704 7.42336 2.02579 7.00201 2C6.58067 2.02579 6.18658 2.21704 5.90562 2.53209C5.62466 2.84714 5.47959 3.26046 5.50201 3.682V11.5C5.47146 12.1292 5.69078 12.7451 6.11222 13.2133C6.53365 13.6816 7.12304 13.9643 7.75201 14H8.25201C8.88098 13.9643 9.47038 13.6816 9.89181 13.2133C10.3132 12.7451 10.5326 12.1292 10.502 11.5V5H11.502V11.5C11.5328 12.3945 11.2083 13.2648 10.5993 13.9207C9.99025 14.5765 9.14633 14.9646 8.25201 15V15Z'/%3E%3C/svg%3E");
	-webkit-mask-repeat: no-repeat;
	mask-repeat: no-repeat;
}
