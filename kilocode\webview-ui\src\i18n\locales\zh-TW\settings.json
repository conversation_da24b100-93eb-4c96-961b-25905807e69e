{"common": {"save": "儲存", "done": "完成", "cancel": "取消", "reset": "重設", "select": "選擇", "add": "新增標頭", "remove": "移除"}, "header": {"title": "設定", "saveButtonTooltip": "儲存變更", "nothingChangedTooltip": "無任何變更", "doneButtonTooltip": "捨棄未儲存的變更並關閉設定面板"}, "unsavedChangesDialog": {"title": "未儲存的變更", "description": "是否要取消變更並繼續？", "cancelButton": "取消", "discardButton": "取消變更"}, "sections": {"providers": "供應商", "autoApprove": "自動核准", "browser": "電腦存取", "checkpoints": "檢查點", "notifications": "通知", "contextManagement": "上下文", "terminal": "終端機", "prompts": "提示詞", "experimental": "實驗性", "language": "語言", "about": "關於 Kilo Code"}, "prompts": {"description": "設定用於快速操作的支援提示詞，如增強提示詞、解釋程式碼和修復問題。這些提示詞幫助 Kilo Code 為常見開發工作提供更好的支援。"}, "codeIndex": {"title": "程式碼庫索引", "enableLabel": "啟用程式碼庫索引", "enableDescription": "<0>程式碼庫索引</0>是一個實驗性功能，使用 AI 嵌入為您的專案建立語義搜尋索引。這使 Kilo Code 能夠透過基於含義而非僅僅關鍵詞來尋找相關程式碼，從而更好地理解和導覽大型程式碼庫。", "providerLabel": "嵌入提供者", "selectProviderPlaceholder": "選擇提供者", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "openaiCompatibleProvider": "OpenAI 相容", "openaiCompatibleBaseUrlLabel": "基礎 URL：", "openaiCompatibleApiKeyLabel": "API 金鑰：", "openaiCompatibleModelDimensionLabel": "嵌入維度：", "openaiCompatibleModelDimensionPlaceholder": "例如，1536", "openaiCompatibleModelDimensionDescription": "模型的嵌入維度（輸出大小）。請查閱您的提供商文件獲取此值。常見值：384、768、1536、3072。", "openaiKeyLabel": "OpenAI 金鑰：", "modelLabel": "模型", "selectModelPlaceholder": "選擇模型", "ollamaUrlLabel": "Ollama URL：", "qdrantUrlLabel": "Qdrant URL", "qdrantKeyLabel": "Qdrant 金鑰：", "startIndexingButton": "開始索引", "clearIndexDataButton": "清除索引資料", "unsavedSettingsMessage": "請先儲存設定再開始索引程序。", "clearDataDialog": {"title": "確定要繼續嗎？", "description": "此操作無法復原。這將永久刪除您的程式碼庫索引資料。", "cancelButton": "取消", "confirmButton": "清除資料"}}, "autoApprove": {"description": "允許 Kilo Code 無需核准即執行操作。僅在您完全信任 AI 並了解相關安全風險時啟用這些設定。", "readOnly": {"label": "讀取", "description": "啟用後，Kilo Code 將自動檢視目錄內容並讀取檔案，無需點選核准按鈕。", "outsideWorkspace": {"label": "包含工作區外的檔案", "description": "允許 Kilo Code 讀取目前工作區外的檔案，無需核准。"}}, "write": {"label": "寫入", "description": "自動建立和編輯文件而無需核准", "delayLabel": "寫入後延遲以允許診斷偵測潛在問題", "outsideWorkspace": {"label": "包含工作區外的檔案", "description": "允許 Kilo Code 在目前工作區外建立和編輯檔案，無需核准。"}}, "browser": {"label": "瀏覽器", "description": "自動執行瀏覽器操作而無需核准 — 注意：僅適用於模型支援電腦使用時"}, "retry": {"label": "重試", "description": "當伺服器回傳錯誤回應時自動重試失敗的 API 請求", "delayLabel": "重試請求前的延遲"}, "mcp": {"label": "MCP", "description": "在 MCP 伺服器檢視中啟用個別 MCP 工具的自動核准（需要此設定和工具的「始終允許」核取方塊）"}, "modeSwitch": {"label": "模式", "description": "自動在不同模式之間切換而無需核准"}, "subtasks": {"label": "子工作", "description": "允許建立和完成子工作而無需核准"}, "execute": {"label": "執行", "description": "自動執行允許的終端機命令而無需核准", "allowedCommands": "允許自動執行的命令", "allowedCommandsDescription": "當「始終核准執行操作」啟用時可以自動執行的命令前綴。新增 * 以允許所有命令（請謹慎使用）。", "commandPlaceholder": "輸入命令前綴（例如 'git '）", "addButton": "新增"}, "showMenu": {"label": "在聊天視圖中顯示自動核准選單", "description": "啟用後，自動核准選單將顯示在聊天視圖底部，提供快速存取自動核准設定"}, "apiRequestLimit": {"title": "最大請求數", "description": "在請求批准以繼續執行工作之前，自動發出此數量的 API 請求。", "unlimited": "無限制"}}, "providers": {"providerDocumentation": "{{provider}} 文件", "configProfile": "配置設定檔", "description": "儲存不同的 API 設定以快速切換供應商和設定。", "apiProvider": "API 供應商", "model": "模型", "nameEmpty": "名稱不能為空", "nameExists": "已存在同名的設定檔", "deleteProfile": "刪除設定檔", "invalidArnFormat": "ARN 格式無效。請檢查上方示例。", "enterNewName": "輸入新名稱", "addProfile": "新增設定檔", "renameProfile": "重新命名設定檔", "newProfile": "新建設定檔", "enterProfileName": "輸入設定檔名稱", "createProfile": "建立設定檔", "cannotDeleteOnlyProfile": "無法刪除唯一的設定檔", "searchPlaceholder": "搜尋設定檔", "noMatchFound": "找不到符合的設定檔", "vscodeLmDescription": "VS Code 語言模型 API 可以讓您使用其他擴充功能（如 GitHub Copilot）提供的模型。最簡單的方式是從 VS Code Marketplace 安裝 Copilot 和 Copilot Chat 擴充套件。", "awsCustomArnUse": "輸入您要使用的模型的有效 Amazon Bedrock ARN。格式範例：", "awsCustomArnDesc": "確保 ARN 中的區域與您上面選擇的 AWS 區域相符。", "openRouterApiKey": "OpenRouter API 金鑰", "getOpenRouterApiKey": "取得 OpenRouter API 金鑰", "apiKeyStorageNotice": "API 金鑰安全儲存於 VSCode 金鑰儲存中", "glamaApiKey": "Glama API 金鑰", "getGlamaApiKey": "取得 Glama API 金鑰", "useCustomBaseUrl": "使用自訂基礎 URL", "useReasoning": "啟用推理", "useHostHeader": "使用自訂 Host 標頭", "useLegacyFormat": "使用舊版 OpenAI API 格式", "customHeaders": "自訂標頭", "headerName": "標頭名稱", "headerValue": "標頭值", "noCustomHeaders": "尚未定義自訂標頭。點擊 + 按鈕以新增。", "requestyApiKey": "Requesty API 金鑰", "refreshModels": {"label": "重新整理模型", "hint": "請重新開啟設定以查看最新模型。", "loading": "正在重新整理模型列表...", "success": "模型列表重新整理成功！", "error": "重新整理模型列表失敗。請再試一次。"}, "getRequestyApiKey": "取得 Requesty API 金鑰", "openRouterTransformsText": "將提示和訊息鏈壓縮到上下文大小 (<a>OpenRouter 轉換</a>)", "anthropicApiKey": "Anthropic API 金鑰", "getAnthropicApiKey": "取得 Anthropic API 金鑰", "anthropicUseAuthToken": "將 Anthropic API 金鑰作為 Authorization 標頭傳遞，而非使用 X-Api-Key", "chutesApiKey": "Chutes API 金鑰", "getChutesApiKey": "取得 Chutes API 金鑰", "deepSeekApiKey": "DeepSeek API 金鑰", "getDeepSeekApiKey": "取得 DeepSeek API 金鑰", "geminiApiKey": "Gemini API 金鑰", "getGroqApiKey": "取得 Groq API 金鑰", "groqApiKey": "Groq API 金鑰", "getGeminiApiKey": "取得 Gemini API 金鑰", "openAiApiKey": "OpenAI API 金鑰", "apiKey": "API 金鑰", "openAiBaseUrl": "基礎 URL", "getOpenAiApiKey": "取得 OpenAI API 金鑰", "mistralApiKey": "Mistral API 金鑰", "getMistralApiKey": "取得 Mistral/Codestral API 金鑰", "codestralBaseUrl": "Codestral 基礎 URL（選用）", "codestralBaseUrlDesc": "設定 Codestral 模型的替代 URL。", "xaiApiKey": "xAI API 金鑰", "getXaiApiKey": "取得 xAI API 金鑰", "litellmApiKey": "LiteLLM API 金鑰", "litellmBaseUrl": "LiteLLM 基礎 URL", "awsCredentials": "AWS 認證", "awsProfile": "AWS Profile", "awsProfileName": "AWS Profile 名稱", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsSessionToken": "AWS 工作階段權杖", "awsRegion": "AWS 區域", "awsCrossRegion": "使用跨區域推論", "awsBedrockVpc": {"useCustomVpcEndpoint": "使用自訂 VPC 端點", "vpcEndpointUrlPlaceholder": "輸入 VPC 端點 URL（選填）", "examples": "範例："}, "enablePromptCaching": "啟用提示快取", "enablePromptCachingTitle": "啟用提示快取以提升支援的模型效能並降低成本。", "cacheUsageNote": "注意：如果您沒有看到快取使用情況，請嘗試選擇其他模型，然後重新選擇您想要的模型。", "vscodeLmModel": "語言模型", "vscodeLmWarning": "注意：此整合功能仍處於實驗階段，各供應商的支援程度可能不同。如果出現模型不支援的錯誤，通常是供應商方面的問題。", "googleCloudSetup": {"title": "要使用 Google Cloud Vertex AI，您需要：", "step1": "1. 建立 Google Cloud 帳戶，啟用 Vertex AI API 並啟用所需的 Claude 模型。", "step2": "2. 安裝 Google Cloud CLI 並設定應用程式預設憑證。", "step3": "3. 或建立具有憑證的服務帳戶。"}, "googleCloudCredentials": "Google Cloud 憑證", "googleCloudKeyFile": "Google Cloud 金鑰檔案路徑", "googleCloudProjectId": "Google Cloud 專案 ID", "googleCloudRegion": "Google Cloud 區域", "lmStudio": {"baseUrl": "基礎 URL（選用）", "modelId": "模型 ID", "speculativeDecoding": "啟用預測性解碼", "draftModelId": "草稿模型 ID", "draftModelDesc": "草稿模型必須來自相同模型系列才能正確運作。", "selectDraftModel": "選擇草稿模型", "noModelsFound": "未找到草稿模型。請確保 LM Studio 以伺服器模式執行。", "description": "LM Studio 允許您在本機電腦執行模型。詳細資訊請參閱快速入門指南。您需要啟動 LM Studio 的本機伺服器功能才能與此擴充功能搭配使用。<span>注意：</span> Kilo Code 使用複雜提示，與 Claude 模型搭配最佳。功能較弱的模型可能無法正常運作。"}, "ollama": {"baseUrl": "基礎 URL（選用）", "modelId": "模型 ID", "description": "Ollama 允許您在本機電腦執行模型。請參閱快速入門指南。", "warning": "注意：Kilo Code 使用複雜提示，與 Claude 模型搭配最佳。功能較弱的模型可能無法正常運作。"}, "unboundApiKey": "Unbound API 金鑰", "getUnboundApiKey": "取得 Unbound API 金鑰", "unboundRefreshModelsSuccess": "模型列表已更新！您現在可以從最新模型中選擇。", "unboundInvalidApiKey": "無效的API金鑰。請檢查您的API金鑰並重試。", "humanRelay": {"description": "不需要 API 金鑰，但使用者需要協助將資訊複製並貼上到網頁聊天 AI。", "instructions": "使用期間會彈出對話框，並自動將目前訊息複製到剪貼簿。您需要將這些內容貼上到網頁版 AI（如 ChatGPT 或 Claude），然後將 AI 的回覆複製回對話框並點選確認按鈕。"}, "openRouter": {"providerRouting": {"title": "OpenRouter 供應商路由", "description": "OpenRouter 會將請求路由到適合您模型的最佳可用供應商。預設情況下，請求會在頂尖供應商之間進行負載平衡以最大化正常運作時間。您也可以為此模型選擇特定的供應商。", "learnMore": "了解更多關於供應商路由的資訊"}}, "customModel": {"capabilities": "設定自訂 OpenAI 相容模型的功能和定價。請謹慎設定模型功能，因為這會影響 Kilo Code 的運作方式。", "maxTokens": {"label": "最大輸出 Token", "description": "模型能在一則回應中產生的最大 Token 數量。（設為 -1 則由伺服器決定最大值）"}, "contextWindow": {"label": "上下文視窗大小", "description": "模型能處理的總 Token 數量（包含輸入和輸出）"}, "imageSupport": {"label": "影像支援", "description": "此模型是否能夠處理和理解影像？"}, "computerUse": {"label": "電腦使用", "description": "此模型是否能夠與瀏覽器互動？（例如 Claude 3.7 Sonnet）"}, "promptCache": {"label": "提示快取", "description": "此模型是否能夠快取提示？"}, "pricing": {"input": {"label": "輸入價格", "description": "輸入/提示每百萬 Token 的費用。這會影響向模型傳送內容和指令時的費用。"}, "output": {"label": "輸出價格", "description": "模型回應每百萬 Token 的費用。這會影響模型產生內容的費用。"}, "cacheReads": {"label": "快取讀取價格", "description": "每百萬 Token 的快取讀取費用。當從快取中取得已儲存的回應時，會收取此費用。"}, "cacheWrites": {"label": "快取寫入價格", "description": "每百萬 Token 的快取寫入費用。當提示首次被儲存至快取時，會收取此費用。"}}, "resetDefaults": "重設為預設值"}, "rateLimitSeconds": {"label": "速率限制", "description": "API 請求間的最短時間"}, "reasoningEffort": {"label": "模型推理強度", "high": "高", "medium": "中", "low": "低"}, "setReasoningLevel": "啟用推理工作量"}, "browser": {"enable": {"label": "啟用瀏覽器工具", "description": "啟用後，Kilo Code 可在使用支援電腦使用的模型時使用瀏覽器與網站互動。 <0>瞭解更多</0>"}, "viewport": {"label": "視窗大小", "description": "選擇瀏覽器互動的視窗大小。這會影響網站的顯示方式和互動方式。", "options": {"largeDesktop": "大型桌面 (1280x800)", "smallDesktop": "小型桌面 (900x600)", "tablet": "平板 (768x1024)", "mobile": "行動裝置 (360x640)"}}, "screenshotQuality": {"label": "截圖品質", "description": "調整瀏覽器截圖的 WebP 品質。數值越高截圖越清晰，但會增加 token 用量。"}, "remote": {"label": "使用遠端瀏覽器連線", "description": "連線到啟用遠端除錯的 Chrome 瀏覽器（--remote-debugging-port=9222）。", "urlPlaceholder": "自訂 URL（例如 http://localhost:9222）", "testButton": "測試連線", "testingButton": "測試中...", "instructions": "請輸入 DevTools Protocol 主機位址，或留空以自動偵測本機 Chrome 執行個體。「測試連線」按鈕將嘗試連線至您提供的自訂 URL，若未提供則會自動偵測。"}}, "checkpoints": {"enable": {"label": "啟用自動檢查點", "description": "啟用後，Kilo Code 將在工作執行期間自動建立檢查點，使審核變更或回到早期狀態變得容易。 <0>瞭解更多</0>"}}, "notifications": {"sound": {"label": "啟用音效", "description": "啟用後，Kilo Code 將為通知和事件播放音效。", "volumeLabel": "音量"}, "tts": {"label": "啟用文字轉語音", "description": "啟用後，Kilo Code 將使用文字轉語音功能朗讀其回應。", "speedLabel": "速度"}}, "contextManagement": {"description": "控制 AI 上下文視窗中要包含哪些資訊，會影響 token 用量和回應品質", "autoCondenseContextPercent": {"label": "觸發智慧上下文壓縮的閾值", "description": "當上下文視窗達到此閾值時，Kilo Code 將自動壓縮它。"}, "condensingApiConfiguration": {"label": "上下文壓縮的API配置", "description": "選擇用於上下文壓縮操作的API配置。留空則使用當前活動的配置。", "useCurrentConfig": "使用當前配置"}, "customCondensingPrompt": {"label": "自訂上下文壓縮提示", "description": "自訂用於上下文壓縮的系統提示。留空則使用預設提示。", "placeholder": "請在此輸入您的自訂上下文壓縮提示...\n\n您可以參考預設提示的結構：\n- 先前對話\n- 目前工作\n- 主要技術概念\n- 相關檔案與程式碼\n- 問題解決\n- 未完成的任務與後續步驟", "reset": "重設為預設值", "hint": "留空 = 使用預設提示"}, "autoCondenseContext": {"name": "自動觸發智慧上下文壓縮"}, "openTabs": {"label": "開啟分頁的上下文限制", "description": "上下文中最多包含多少個 VS Code 開啟的分頁。數值越高提供的上下文越多，但 token 用量也會增加。"}, "workspaceFiles": {"label": "工作區檔案的上下文限制", "description": "目前工作目錄中最多包含多少個檔案。數值越高提供的上下文越多，但 token 用量也會增加。"}, "rooignore": {"label": "在列表和搜尋中顯示被 .kilocodeignore 排除的檔案", "description": "啟用後，符合 .kilocodeignore 規則的檔案會在列表中顯示並標示鎖定圖示。停用後，這些檔案將完全從檔案列表和搜尋結果中隱藏。"}, "maxReadFile": {"label": "檔案讀取自動截斷閾值", "description": "當模型未指定起始/結束值時，Kilo Code 讀取的行數。如果此數值小於檔案總行數，Kilo Code 將產生程式碼定義的行號索引。特殊情況：-1 指示 Kilo Code 讀取整個檔案（不建立索引），0 指示不讀取任何行並僅提供行索引以取得最小上下文。較低的值可最小化初始上下文使用，允許後續精確的行範圍讀取。明確指定起始/結束的請求不受此設定限制。 <0>瞭解更多</0>", "lines": "行", "always_full_read": "始終讀取整個檔案"}, "maxConcurrentFileReads": {"label": "並行檔案讀取限制", "description": "read_file 工具可以同時處理的最大檔案數。較高的值可能會加快讀取多個小檔案的速度，但會增加記憶體使用量。"}}, "terminal": {"basic": {"label": "終端機設定：基本", "description": "基本終端機設定"}, "advanced": {"label": "終端機設定：進階", "description": "以下選項可能需要重新啟動終端機才能套用設定"}, "outputLineLimit": {"label": "終端機輸出行數限制", "description": "執行命令時終端機輸出的最大行數。超過此限制時，會從中間移除多餘的行數，以節省 token 用量。 <0>瞭解更多</0>"}, "shellIntegrationTimeout": {"label": "終端機 Shell 整合逾時", "description": "執行命令前等待 Shell 整合初始化的最長時間。如果您的 Shell 啟動較慢，且終端機出現「Shell 整合無法使用」的錯誤訊息，可能需要提高此數值。 <0>瞭解更多</0>"}, "shellIntegrationDisabled": {"label": "停用終端機 Shell 整合", "description": "如果終端機指令無法正常運作或看到 'Shell Integration Unavailable' 錯誤，請啟用此項。這會使用較簡單的方法執行指令，繞過一些進階終端機功能。 <0>瞭解更多</0>"}, "commandDelay": {"label": "終端機命令延遲", "description": "命令執行後添加的延遲時間（毫秒）。預設值為 0 時完全停用延遲。這可以幫助確保在有計時問題的終端機中完整擷取命令輸出。在大多數終端機中，這是透過設定 `PROMPT_COMMAND='sleep N'` 實現的，而 PowerShell 會在每個命令結尾加入 `start-sleep`。最初是為了解決 VSCode 錯誤#237208，現在可能不再需要。 <0>瞭解更多</0>"}, "compressProgressBar": {"label": "壓縮進度條輸出", "description": "啟用後，將處理包含歸位字元 (\\r) 的終端機輸出，模擬真實終端機顯示內容的方式。這會移除進度條的中間狀態，只保留最終狀態，為更重要的資訊節省上下文空間。 <0>瞭解更多</0>"}, "powershellCounter": {"label": "啟用 PowerShell 計數器解決方案", "description": "啟用後，會在 PowerShell 命令中加入計數器以確保命令正確執行。這有助於解決可能存在輸出擷取問題的 PowerShell 終端機。 <0>瞭解更多</0>"}, "zshClearEolMark": {"label": "清除 ZSH 行尾標記", "description": "啟用後，透過設定 PROMPT_EOL_MARK='' 清除 ZSH 行尾標記。這可以防止命令輸出以特殊字元（如 '%'）結尾時的解析問題。 <0>瞭解更多</0>"}, "zshOhMy": {"label": "啟用 Oh My Zsh 整合", "description": "啟用後，設定 ITERM_SHELL_INTEGRATION_INSTALLED=Yes 以啟用 Oh My Zsh shell 整合功能。套用此設定可能需要重新啟動 IDE。 <0>瞭解更多</0>"}, "zshP10k": {"label": "啟用 Powerlevel10k 整合", "description": "啟用後，設定 POWERLEVEL9K_TERM_SHELL_INTEGRATION=true 以啟用 Powerlevel10k shell 整合功能。 <0>瞭解更多</0>"}, "zdotdir": {"label": "啟用 ZDOTDIR 處理", "description": "啟用後將建立暫存目錄用於 ZDOTDIR，以正確處理 zsh shell 整合。這確保 VSCode shell 整合能與 zsh 正常運作，同時保留您的 zsh 設定。 <0>瞭解更多</0>"}, "inheritEnv": {"label": "繼承環境變數", "description": "啟用後，終端機將從 VSCode 父程序繼承環境變數，如使用者設定檔中定義的 shell 整合設定。這直接切換 VSCode 全域設定 `terminal.integrated.inheritEnv`。 <0>瞭解更多</0>"}}, "advanced": {"diff": {"label": "透過差異比對編輯", "description": "啟用後，Kilo Code 可更快速地編輯檔案，並自動拒絕不完整的整檔覆寫。搭配最新的 Claude 4 Sonnet 模型效果最佳。", "strategy": {"label": "差異比對策略", "options": {"standard": "標準（單一區塊）", "multiBlock": "實驗性：多區塊差異", "unified": "實驗性：統一差異"}, "descriptions": {"standard": "標準策略一次只修改一個程式碼區塊。", "unified": "統一差異策略會嘗試多種比對方式，並選擇最佳方案。", "multiBlock": "多區塊策略可在單一請求中更新檔案內的多個程式碼區塊。"}}, "matchPrecision": {"label": "比對精確度", "description": "此滑桿控制套用差異時程式碼區段的比對精確度。較低的數值允許更彈性的比對，但也會增加錯誤取代的風險。使用低於 100% 的數值時請特別謹慎。"}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "使用實驗性統一差異比對策略", "description": "啟用實驗性的統一差異比對策略。此策略可能減少因模型錯誤而導致的重試次數，但也可能導致意外行為或錯誤的編輯。請務必了解風險，並願意仔細檢查所有變更後再啟用。"}, "SEARCH_AND_REPLACE": {"name": "使用實驗性搜尋與取代工具", "description": "啟用實驗性的搜尋與取代工具，允許 Kilo Code 在單一請求中取代多個符合的內容。"}, "INSERT_BLOCK": {"name": "使用實驗性插入內容工具", "description": "啟用實驗性的插入內容工具，允許 Kilo Code 直接在指定行號插入內容，而無需產生差異比對。"}, "POWER_STEERING": {"name": "使用實驗性「動力輔助」模式", "description": "啟用後，Kilo Code 將更頻繁地提醒模型目前模式的詳細設定。這能讓模型更嚴格遵守角色定義和自訂指令，但每則訊息會使用更多 token。"}, "AUTOCOMPLETE": {"name": "使用實驗性「自動完成」功能", "description": "啟用後，Kilo Code 會在您輸入時提供內嵌程式碼建議。"}, "MULTI_SEARCH_AND_REPLACE": {"name": "使用實驗性多區塊差異比對工具", "description": "啟用後，Kilo Code 將使用多區塊差異比對工具，嘗試在單一請求中更新檔案內的多個程式碼區塊。"}, "CONCURRENT_FILE_READS": {"name": "啟用並行檔案讀取", "description": "啟用後，Kilo Code 可以在單一請求中讀取多個檔案（最多 15 個檔案）。停用後，Kilo Code 必須逐一讀取檔案。在使用能力較弱的模型或希望對檔案存取有更多控制時，停用此功能可能會有所幫助。"}}, "promptCaching": {"label": "停用提示詞快取", "description": "勾選後，Kilo Code 將不會為此模型使用提示詞快取。"}, "temperature": {"useCustom": "使用自訂溫度", "description": "控制模型回應的隨機性", "rangeDescription": "較高值使輸出更隨機，較低值更確定"}, "modelInfo": {"supportsImages": "支援影像", "noImages": "不支援影像", "supportsComputerUse": "支援電腦使用", "noComputerUse": "不支援電腦使用", "supportsPromptCache": "支援提示快取", "noPromptCache": "不支援提示快取", "maxOutput": "最大輸出", "inputPrice": "輸入價格", "outputPrice": "輸出價格", "cacheReadsPrice": "快取讀取價格", "cacheWritesPrice": "快取寫入價格", "enableStreaming": "啟用串流輸出", "enableR1Format": "啟用 R1 模型參數", "enableR1FormatTips": "使用 QWQ 等 R1 模型時必須啟用，以避免發生 400 錯誤", "useAzure": "使用 Azure", "azureApiVersion": "設定 Azure API 版本", "gemini": {"freeRequests": "* 每分鐘可免費使用 {{count}} 次請求，超過後將依提示大小計費。", "pricingDetails": "詳細資訊請參閱定價說明。", "billingEstimate": "* 費用為估算值 - 實際費用取決於提示大小。"}}, "modelPicker": {"automaticFetch": "此擴充功能會自動從 <serviceLink>{{serviceName}}</serviceLink> 取得最新的可用模型清單。如果不確定要選哪個模型，建議使用 <defaultModelLink>{{defaultModelId}}</defaultModelLink>，這是與 Kilo Code 最佳搭配的模型。您也可以搜尋「free」來檢視目前可用的免費選項。", "label": "模型", "searchPlaceholder": "搜尋", "noMatchFound": "找不到符合的項目", "useCustomModel": "使用自訂模型：{{modelId}}"}, "footer": {"feedback": "若您有任何問題或建議，歡迎至 <githubLink>github.com/Kilo-Org/kilocode</githubLink> 提出 issue，或加入 <redditLink>reddit.com/r/kilocode</redditLink> 或 <discordLink>kilocode.ai/discord</discordLink> 討論。", "support": "如有財務相關問題，請聯絡客戶支援 <supportLink><EMAIL></supportLink>", "telemetry": {"label": "允許匿名錯誤與使用情況回報", "description": "透過傳送匿名的使用資料與錯誤回報，協助改善 Kilo Code。我們絕不會傳送您的程式碼、提示或個人資訊。詳細資訊請參閱我們的隱私權政策。"}, "settings": {"import": "匯入", "export": "匯出", "reset": "重設"}}, "thinkingBudget": {"maxTokens": "最大 token 數", "maxThinkingTokens": "最大思考 token 數"}, "validation": {"apiKey": "請提供有效的 API 金鑰。", "awsRegion": "請選擇要用於 Amazon Bedrock 的區域。", "googleCloud": "請提供有效的 Google Cloud 專案 ID 和區域。", "modelId": "請提供有效的模型 ID。", "modelSelector": "請提供有效的模型選擇器。", "openAi": "請提供有效的基礎 URL、API 金鑰和模型 ID。", "arn": {"invalidFormat": "ARN 格式無效，請檢查格式要求。", "regionMismatch": "警告：您 ARN 中的區域 ({{arnRegion}}) 與您選擇的區域 ({{region}}) 不符，可能導致存取問題。系統將使用 ARN 中指定的區域。"}, "modelAvailability": "您指定的模型 ID ({{modelId}}) 目前無法使用，請選擇其他模型。", "providerNotAllowed": "供應商 '{{provider}}' 不允許用於您的組織。", "modelNotAllowed": "模型 '{{model}}' 不允許用於供應商 '{{provider}}'，您的組織不允許", "profileInvalid": "此設定檔包含您的組織不允許的供應商或模型"}, "placeholders": {"apiKey": "請輸入 API 金鑰...", "profileName": "請輸入設定檔名稱", "accessKey": "請輸入存取金鑰...", "secretKey": "請輸入金鑰...", "sessionToken": "請輸入工作階段權杖...", "credentialsJson": "請輸入憑證 JSON...", "keyFilePath": "請輸入金鑰檔案路徑...", "projectId": "請輸入專案 ID...", "customArn": "請輸入 ARN（例：arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model）", "baseUrl": "請輸入基礎 URL...", "modelId": {"lmStudio": "例：meta-llama-3.1-8b-instruct", "lmStudioDraft": "例：lmstudio-community/llama-3.2-1b-instruct", "ollama": "例：llama3.1"}, "numbers": {"maxTokens": "例：4096", "contextWindow": "例：128000", "inputPrice": "例：0.0001", "outputPrice": "例：0.0002", "cacheWritePrice": "例：0.00005"}}, "defaults": {"ollamaUrl": "預設：http://localhost:11434", "lmStudioUrl": "預設：http://localhost:1234", "geminiUrl": "預設：https://generativelanguage.googleapis.com"}, "labels": {"customArn": "自訂 ARN", "useCustomArn": "使用自訂 ARN..."}}