# Improve your prompt

Click the stars icon to make your prompt clearer and more complete. Kilo Code will rewrite it to get better results.

# Add files or folders for context

Mention files or folders in your prompt. Kilo Code will use them to better understand and respond to your request.

Click the paperclip in the text area or use @ to add context.

# Add custom modes

Go beyond the default modes by creating your own. Tailor them to your workflow with specific instructions or focus areas.

Click mode switcher in the text area or use / to switch modes.
