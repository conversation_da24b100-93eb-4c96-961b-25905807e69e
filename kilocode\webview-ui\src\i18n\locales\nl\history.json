{"recentTasks": "Taken", "viewAll": "Alle taken weergeven", "tokens": "Tokens: ↑{{in}} ↓{{out}}", "cache": "Cache: +{{writes}} → {{reads}}", "apiCost": "API-kosten: ${{cost}}", "history": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitSelectionMode": "Selectiemodus verlaten", "enterSelectionMode": "Selectiemodus starten", "done": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Geschiedenis doorzoeken...", "newest": "Nieuwste", "oldest": "Oudste", "mostExpensive": "<PERSON><PERSON><PERSON>", "mostTokens": "Meeste tokens", "mostRelevant": "Meest relevant", "deleteTaskTitle": "<PERSON><PERSON> verwijderen (<PERSON><PERSON> + <PERSON><PERSON> om bevestiging over te slaan)", "tokensLabel": "Tokens:", "cacheLabel": "Cache:", "apiCostLabel": "API-kosten:", "copyPrompt": "Prompt kopiëren", "exportTask": "Taak exporteren", "deleteTask": "Taak verwi<PERSON>en", "deleteTaskMessage": "Weet je zeker dat je deze taak wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Verwijderen", "exitSelection": "<PERSON><PERSON> verlaten", "selectionMode": "Selectiemodus", "deselectAll": "Alles deselecteren", "selectAll": "Alles selecteren", "selectedItems": "Geselecteerd {{selected}}/{{total}} items", "clearSelection": "<PERSON><PERSON> wissen", "deleteSelected": "Geselecteerde verwijderen", "deleteTasks": "Taken verwijderen", "confirmDeleteTasks": "Weet je zeker dat je {{count}} taken wilt verwijderen?", "deleteTasksWarning": "Verwijderde taken kunnen niet worden hersteld. Zorg ervoor dat je wilt doorgaan.", "deleteTaskFavoritedWarning": "Deze taak is favoriet. Weet je zeker dat je deze wilt verwijderen?", "deleteTasksFavoritedWarning": "{{count}} <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> taken zijn favoriet. Weet je zeker dat je deze wilt verwijderen?", "deleteItems": "Verwi<PERSON>der {{count}} items", "showAllWorkspaces": "Toon taken van alle werkruimtes", "showFavoritesOnly": "Toon alleen favorieten"}