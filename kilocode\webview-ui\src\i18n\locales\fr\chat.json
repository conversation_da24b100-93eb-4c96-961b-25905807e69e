{"greeting": "Que peut faire Kilo Code pour vous ?", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Voir plus", "seeLess": "Voir moins", "tokens": "Tokens :", "cache": "Cache :", "apiCost": "Coût API :", "contextWindow": "<PERSON><PERSON><PERSON> du contexte :", "closeAndStart": "<PERSON><PERSON><PERSON> la tâche et en commencer une nouvelle", "export": "Exporter l'historique des tâches", "delete": "Supprimer la tâche (Shift + Clic pour ignorer la confirmation)", "condenseContext": "Condenser intelligemment le contexte"}, "history": {"title": "Historique"}, "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON><PERSON>", "tokenProgress": {"availableSpace": "Espace disponible : {{amount}} tokens", "tokensUsed": "Tokens utilisés : {{used}} sur {{total}}", "reservedForResponse": "Réservé pour la réponse du modèle : {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Tenter à nouveau l'opération"}, "startNewTask": {"title": "Commencer une nouvelle tâche", "tooltip": "<PERSON><PERSON><PERSON><PERSON> une nouvelle tâche"}, "reportBug": {"title": "Signaler un bug"}, "proceedAnyways": {"title": "Continuer quand même", "tooltip": "Continuer pendant l'exécution de la commande"}, "save": {"title": "Enregistrer", "tooltip": "Sauvegarder les modifications du fichier"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Rejeter cette action"}, "completeSubtaskAndReturn": "Terminer la sous-tâche et revenir", "approve": {"title": "Approuver", "tooltip": "Approuver cette action"}, "runCommand": {"title": "Exécuter la commande", "tooltip": "Exécuter cette commande"}, "proceedWhileRunning": {"title": "Continuer pendant l'exécution", "tooltip": "Continuer malgré les avertissements"}, "killCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> la commande", "tooltip": "<PERSON><PERSON><PERSON><PERSON> la commande actuelle"}, "resumeTask": {"title": "Reprendre la tâche", "tooltip": "Continuer la tâche actuelle"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Te<PERSON>iner la tâche actuelle"}, "cancel": {"title": "Annuler", "tooltip": "Annuler l'opération actuelle"}, "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> jusqu'au bas du chat", "about": "<PERSON><PERSON><PERSON><PERSON>, refactoriser et déboguer du code avec l'assistance de l'IA. Consultez notre <DocsLink>documentation</DocsLink> pour en savoir plus.", "onboarding": "Grâce aux dernières avancées en matière de capacités de codage agent, je peux gérer des tâches complexes de développement logiciel étape par étape. Avec des outils qui me permettent de créer et d'éditer des fichiers, d'explorer des projets complexes, d'utiliser le navigateur et d'exécuter des commandes de terminal (après votre autorisation), je peux vous aider de manières qui vont au-delà de la complétion de code ou du support technique. Je peux même utiliser MCP pour créer de nouveaux outils et étendre mes propres capacités.", "rooTips": {"boomerangTasks": {"title": "Tâches Boomerang", "description": "Divisez les tâches en parties plus petites et gérables."}, "stickyModels": {"title": "Modes persistants", "description": "Chaque mode se souvient de votre dernier modèle utilisé"}, "tools": {"title": "Outils", "description": "Permettez à l'IA de résoudre des problèmes en naviguant sur le Web, en exécutant des commandes, et plus encore."}, "customizableModes": {"title": "Modes personnalisables", "description": "Des personas spécialisés avec leurs propres comportements et modèles assignés"}}, "selectMode": "Sélectionner le mode d'interaction", "selectApiConfig": "Sélectionner la configuration de l'API", "enhancePrompt": "Améliorer la requête avec un contexte supplémentaire", "addImages": "Ajouter des images au message", "sendMessage": "Envoyer le message", "typeMessage": "Écrivez un message...", "typeTask": "<PERSON><PERSON><PERSON><PERSON>, trouver, demander quelque chose", "addContext": "@ pour ajouter du contexte, / pour changer de mode", "dragFiles": "maintenir Maj pour glisser des fichiers", "dragFilesImages": "maintenir Maj pour glisser des fichiers/images", "enhancePromptDescription": "Le bouton 'Améliorer la requête' aide à améliorer votre demande en fournissant un contexte supplémentaire, des clarifications ou des reformulations. Essayez de taper une demande ici et cliquez à nouveau sur le bouton pour voir comment cela fonctionne.", "errorReadingFile": "<PERSON><PERSON>ur lors de la lecture du fichier :", "noValidImages": "Aucune image valide n'a été traitée", "separator": "Séparateur", "edit": "Éditer...", "forNextMode": "pour le prochain mode", "error": "<PERSON><PERSON><PERSON>", "diffError": {"title": "Modification échouée"}, "troubleMessage": "Kilo Code rencontre des difficultés...", "apiRequest": {"title": "Requête API", "failed": "Échec de la requête API", "streaming": "Requête API...", "cancelled": "Requête API annulée", "streamingFailed": "Échec du streaming API"}, "checkpoint": {"initial": "Point de contrôle initial", "regular": "Point de contrôle", "initializingWarning": "Initialisation du point de contrôle en cours... Si cela prend trop de temps, tu peux désactiver les points de contrôle dans les <settingsLink>paramètres</settingsLink> et redémarrer ta tâche.", "menu": {"viewDiff": "Voir les différences", "restore": "Restaurer le point de contrôle", "restoreFiles": "Restaurer les fichiers", "restoreFilesDescription": "Restaure les fichiers de votre projet à un instantané pris à ce moment.", "restoreFilesAndTask": "Restaurer fichiers et tâche", "confirm": "Confirmer", "cancel": "Annuler", "cannotUndo": "Cette action ne peut pas être annulée.", "restoreFilesAndTaskDescription": "Restaure les fichiers de votre projet à un instantané pris à ce moment et supprime tous les messages après ce point."}, "current": "Actuel"}, "fileOperations": {"wantsToRead": "Kilo Code veut lire ce fichier :", "wantsToReadOutsideWorkspace": "Kilo Code veut lire ce fichier en dehors de l'espace de travail :", "didRead": "Kilo Code a lu ce fichier :", "wantsToEdit": "Kilo Code veut éditer ce fichier :", "wantsToEditOutsideWorkspace": "Kilo Code veut éditer ce fichier en dehors de l'espace de travail :", "wantsToCreate": "Kilo Code veut créer un nouveau fichier :", "wantsToSearchReplace": "Kilo Code veut effectuer une recherche et remplacement sur ce fichier :", "didSearchReplace": "Kilo Code a effectué une recherche et remplacement sur ce fichier :", "wantsToInsert": "Kilo Code veut insérer du contenu dans ce fichier :", "wantsToInsertWithLineNumber": "Kilo Code veut insérer du contenu dans ce fichier à la ligne {{lineNumber}} :", "wantsToInsertAtEnd": "Kilo Code veut ajouter du contenu à la fin de ce fichier :", "wantsToReadAndXMore": "Kilo Code veut lire ce fichier et {{count}} de plus :", "wantsToReadMultiple": "Kilo Code souhaite lire plusieurs fichiers :"}, "instructions": {"wantsToFetch": "Kilo Code veut récupérer des instructions détaillées pour aider à la tâche actuelle"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code veut voir les fichiers de premier niveau dans ce répertoire :", "didViewTopLevel": "Kilo Code a vu les fichiers de premier niveau dans ce répertoire :", "wantsToViewRecursive": "Kilo Code veut voir récursivement tous les fichiers dans ce répertoire :", "didViewRecursive": "Kilo Code a vu récursivement tous les fichiers dans ce répertoire :", "wantsToViewDefinitions": "Kilo Code veut voir les noms de définitions de code source utilisés dans ce répertoire :", "didViewDefinitions": "Kilo Code a vu les noms de définitions de code source utilisés dans ce répertoire :", "wantsToSearch": "Kilo Code veut rechercher dans ce répertoire <code>{{regex}}</code> :", "didSearch": "Kilo Code a recherché dans ce répertoire <code>{{regex}}</code> :"}, "commandOutput": "Sortie de commande", "response": "Réponse", "arguments": "Arguments", "mcp": {"wantsToUseTool": "Kilo Code veut utiliser un outil sur le serveur MCP {{serverName}} :", "wantsToAccessResource": "Kilo Code veut accéder à une ressource sur le serveur MCP {{serverName}} :"}, "modes": {"wantsToSwitch": "Kilo Code veut passer au mode <code>{{mode}}</code>", "wantsToSwitchWithReason": "Kilo Code veut passer au mode <code>{{mode}}</code> car : {{reason}}", "didSwitch": "Kilo Code est passé au mode <code>{{mode}}</code>", "didSwitchWithReason": "Kilo Code est passé au mode <code>{{mode}}</code> car : {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code veut créer une nouvelle sous-tâche en mode <code>{{mode}}</code> :", "wantsToFinish": "Kilo Code veut terminer cette sous-tâche", "newTaskContent": "Instructions de la sous-tâche", "completionContent": "Sous-tâche terminée", "resultContent": "Résultats de la sous-tâche", "defaultResult": "Veuillez continuer avec la tâche suivante.", "completionInstructions": "Sous-tâche terminée ! Vous pouvez examiner les résultats et suggérer des corrections ou les prochaines étapes. Si tout semble bon, confirmez pour retourner le résultat à la tâche parente."}, "questions": {"hasQuestion": "Kilo Code a une question :"}, "taskCompleted": "Tâche terminée", "powershell": {"issues": "Il semble que vous rencontriez des problèmes avec Windows PowerShell, ve<PERSON><PERSON>z consulter ce"}, "autoApprove": {"title": "Auto-approbation :", "none": "Aucune", "description": "L'auto-approbation permet à Kilo Code d'effectuer des actions sans demander d'autorisation. Activez-la uniquement pour les actions auxquelles vous faites entièrement confiance. Configuration plus détaillée disponible dans les <settingsLink>Paramètres</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexte condensé", "condensing": "Condensation du contexte...", "errorHeader": "Échec de la condensation du contexte", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON>r vers l'entrée (ou Shift + clic)"}, "announcement": {"title": "🎉 Roo Code {{version}} est sortie", "description": "Roo Code {{version}} apporte de puissantes nouvelles fonctionnalités et améliorations basées sur vos retours.", "whatsNew": "<PERSON><PERSON><PERSON> de <PERSON>uf", "feature1": "<bold>Condensation Intelligente du Contexte Activée par Défaut</bold> : La condensation du contexte est maintenant activée par défaut avec des paramètres configurables pour quand la condensation automatique se produit", "feature2": "<bold>Bouton de Condensation Manuelle</bold> : Nouveau bouton dans l'en-tête des tâches qui te permet de déclencher manuellement la condensation du contexte à tout moment", "feature3": "<bold>Paramètres de Condensation Améliorés</bold> : Ajuste quand et comment la condensation automatique se produit via les <contextSettingsLink>Paramètres de Contexte</contextSettingsLink>", "hideButton": "Masquer l'annonce", "detailsDiscussLinks": "Obtenez plus de détails et participez aux discussions sur <discordLink>Discord</discordLink> et <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Kilo Code veut utiliser le navigateur :", "consoleLogs": "Journaux de console", "noNewLogs": "(Pas de nouveaux journaux)", "screenshot": "Capture d'écran du navigateur", "cursor": "<PERSON><PERSON>", "navigation": {"step": "Étape {{current}} sur {{total}}", "previous": "Précédent", "next": "Suivant"}, "sessionStarted": "Session de navigateur démar<PERSON>e", "actions": {"title": "Action de navigation : ", "launch": "<PERSON><PERSON> le <PERSON> sur {{url}}", "click": "Cliquer ({{coordinate}})", "type": "<PERSON><PERSON> \"{{text}}\"", "scrollDown": "Dé<PERSON>ler vers le bas", "scrollUp": "Dé<PERSON><PERSON> vers le haut", "close": "<PERSON><PERSON><PERSON> le <PERSON>ur"}}, "codeblock": {"tooltips": {"expand": "Développer le bloc de code", "collapse": "Réduire le bloc de code", "enable_wrap": "<PERSON><PERSON> le retour à la ligne", "disable_wrap": "Désactiver le retour à la ligne", "copy_code": "Copier le code"}}, "systemPromptWarning": "AVERTISSEMENT : Remplacement d'instructions système personnalisées actif. Cela peut gravement perturber la fonctionnalité et provoquer un comportement imprévisible.", "profileViolationWarning": "Le profil actuel enfreint les paramètres de votre organisation", "shellIntegration": {"title": "Avertissement d'exécution de commande", "description": "Votre commande est exécutée sans l'intégration shell du terminal VSCode. Pour supprimer cet avertissement, vous pouvez désactiver l'intégration shell dans la section <strong>Terminal</strong> des <settingsLink>paramètres de Kilo Code</settingsLink> ou résoudre les problèmes d'intégration du terminal VSCode en utilisant le lien ci-dessous.", "troubleshooting": "Cliquez ici pour la documentation d'intégration shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite de requêtes auto-approuvées atteinte", "description": "Kilo Code a atteint la limite auto-approuvée de {{count}} requête(s) API. Souhaitez-vous réinitialiser le compteur et poursuivre la tâche ?", "button": "Réinitialiser et continuer"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code veut rechercher dans la base de code <code>{{query}}</code> :", "wantsToSearchWithPath": "Kilo Code veut rechercher dans la base de code <code>{{query}}</code> dans <code>{{path}}</code> :", "didSearch": "{{count}} résultat(s) trouvé(s) pour <code>{{query}}</code> :"}, "read-batch": {"approve": {"title": "Tout approuver"}, "deny": {"title": "Tout refuser"}}}