{"welcome": {"greeting": "<PERSON><PERSON> Code có thể làm gì cho bạn?", "introText": "Tôi đã được đào tạo trên các mô hình <PERSON> nhanh nhất hiện có và sẵn sàng tạo mã cho bạn với tốc độ cực nhanh!", "ctaButton": "<PERSON><PERSON>ng thử Kilo Code miễn phí", "manualModeButton": "Sử dụng khóa API của riêng bạn"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON><PERSON> tín dụng", "lowBalance": "Số d<PERSON> của bạn thấp"}, "notifications": {"toolRequest": "<PERSON><PERSON><PERSON> cầu công cụ đang chờ phê duyệt", "browserAction": "<PERSON><PERSON><PERSON> động trình duyệt đang chờ phê duyệt", "command": "<PERSON><PERSON><PERSON> đang chờ phê du<PERSON>t"}, "settings": {"sections": {"mcp": "<PERSON><PERSON><PERSON> chủ MCP"}, "provider": {"apiKey": "Khóa API Kilo Code", "login": "<PERSON><PERSON><PERSON> nh<PERSON>p v<PERSON><PERSON>", "logout": "<PERSON><PERSON>ng xuất khỏi Kilo Code"}}, "chat": {"condense": {"wantsToCondense": "<PERSON>lo <PERSON> muốn tóm tắt cuộc trò chuyện của bạn", "condenseConversation": "<PERSON><PERSON><PERSON> tắt cuộc trò chuy<PERSON>n"}}, "newTaskPreview": {"task": "Nhiệm vụ"}, "profile": {"title": "<PERSON><PERSON> sơ", "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "logOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "currentBalance": "SỐ DƯ HIỆN TẠI", "loading": "<PERSON><PERSON> tả<PERSON>...", "signUp": {"title": "Đăng ký Kilo Code", "description": "Đăng ký Kilo Code sẽ cung cấp cho bạn tín dụng miễn phí để bắt đầu. Sử dụng tín dụng của bạn để khám phá các tính năng, thử nghiệm các mô hình mới nhất và tốt nhất, và trải nghiệm lợi ích của Kilo Code mà không cần cam kết.", "termsAndPrivacy": "Bằng cách tiế<PERSON> tụ<PERSON>, bạn đồng ý với <termsLink>Đi<PERSON>u khoản Dịch vụ</termsLink> và <privacyLink><PERSON><PERSON><PERSON> s<PERSON>ch <PERSON> mật.</privacyLink>"}}}